#!/bin/bash
# Production Deployment Script for Workout Planning Feature
# =========================================================

set -e  # Exit on any error

echo "🏋️  Workout Tracker - Production Deployment"
echo "============================================="
echo ""

# Check if we're in the right directory
if [[ ! -f "workout_planning_migration.py" ]]; then
    echo "❌ Error: workout_planning_migration.py not found"
    echo "Please run this script from the directory containing the migration files"
    exit 1
fi

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1-2)
echo "🐍 Python version: $python_version"

if [[ $(echo "$python_version >= 3.7" | bc -l) -eq 0 ]]; then
    echo "❌ Error: Python 3.7+ required, found $python_version"
    exit 1
fi

# Install dependencies
echo "📦 Installing migration dependencies..."
pip3 install -r migration_requirements.txt

# Check environment variables
echo "🔍 Checking environment variables..."

if [[ -n "$DATABASE_URL" ]]; then
    echo "✅ DATABASE_URL is set"
elif [[ -n "$DB_PASSWORD" ]]; then
    echo "✅ Individual DB variables are set"
    echo "   Host: ${DB_HOST:-localhost}"
    echo "   Port: ${DB_PORT:-5432}"
    echo "   Database: ${DB_NAME:-workout_db}"
    echo "   User: ${DB_USER:-postgres}"
else
    echo "❌ Error: Either DATABASE_URL or DB_PASSWORD must be set"
    echo ""
    echo "Set DATABASE_URL:"
    echo "  export DATABASE_URL='postgresql://user:pass@host:port/dbname'"
    echo ""
    echo "Or set individual variables:"
    echo "  export DB_HOST='your-host'"
    echo "  export DB_PORT='5432'"
    echo "  export DB_NAME='workout_db'"
    echo "  export DB_USER='postgres'"
    echo "  export DB_PASSWORD='your-password'"
    exit 1
fi

echo ""
echo "🚀 Ready to run migration!"
echo ""
echo "This will:"
echo "  ✅ Create workout_plans table"
echo "  ✅ Create planned_workouts table"
echo "  ✅ Add planned_workout_id column to workouts"
echo "  ✅ Verify all changes"
echo ""

read -p "Continue with migration? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled."
    exit 0
fi

# Run migration
echo "🔧 Running migration..."
python3 workout_planning_migration.py

echo ""
echo "🎉 Migration completed successfully!"
echo ""
echo "Next steps:"
echo "1. Deploy your updated application code"
echo "2. Test the workout planning feature"
echo "3. Users can now create and manage workout plans!"
echo ""
echo "📚 Feature includes:"
echo "   • AI-powered workout plan generation"
echo "   • Scheduled workout tracking"
echo "   • Automatic completion linking"
echo "   • Progress monitoring"
