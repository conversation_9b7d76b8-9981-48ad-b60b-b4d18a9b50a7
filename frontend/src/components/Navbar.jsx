// frontend/src/components/Navbar.jsx
import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const Navbar = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeWorkout, setActiveWorkout] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const menuRef = useRef(null);

  // Check for active workout
  useEffect(() => {
    const checkActiveWorkout = async () => {
      if (user) {
        try {
          setLoading(true);
          const workout = await api.getWorkoutInProgress();
          setActiveWorkout(workout);
        } catch (error) {
          console.error('Error checking for active workout:', error);
          // Don't show error in navbar, just log it
        } finally {
          setLoading(false);
        }
      }
    };

    checkActiveWorkout();
  }, [user, location.pathname]);

  // Close menus when navigating
  useEffect(() => {
    setIsMenuOpen(false);
    setIsUserMenuOpen(false);
  }, [location.pathname]);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMenuOpen && menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
      if (isUserMenuOpen && !event.target.closest('.user-menu-container')) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen, isUserMenuOpen]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    if (isUserMenuOpen) setIsUserMenuOpen(false);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
    if (isMenuOpen) setIsMenuOpen(false);
  };

  return (
    <nav className="bg-primary-600 shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="text-white font-bold text-xl flex items-center">
              <i className="fas fa-dumbbell mr-2"></i>
              <span className="hidden sm:inline">FitTrack</span>
            </Link>
          </div>

          {/* Right side navigation items */}
          <div className="flex items-center space-x-4">
            {/* Active workout button - always visible */}
            {user && activeWorkout && (
              <Link
                to="/active-workout"
                className="bg-white text-primary-600 px-3 py-1 rounded-md font-medium hover:bg-primary-50 transition-colors flex items-center"
              >
                <span className="h-2 w-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                <span className="text-sm">Active Workout</span>
              </Link>
            )}

            {/* User menu for desktop */}
            {user ? (
              <div className="relative user-menu-container">
                <button
                  onClick={toggleUserMenu}
                  className="text-white hover:text-primary-200 transition-colors flex items-center"
                >
                  <i className="fas fa-user-circle mr-1"></i>
                  <span className="hidden sm:inline">{user.username}</span>
                </button>
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 animate-slideIn">
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-sign-out-alt mr-1"></i> Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="hidden sm:flex items-center space-x-2">
                <Link
                  to="/login"
                  className="text-white hover:text-primary-200 transition-colors"
                >
                  <i className="fas fa-sign-in-alt mr-1"></i> Login
                </Link>
                <Link
                  to="/register"
                  className="bg-white text-primary-600 px-3 py-1 rounded-md font-medium hover:bg-primary-50 transition-colors"
                >
                  <i className="fas fa-user-plus mr-1"></i> Register
                </Link>
              </div>
            )}

            {/* Hamburger menu button - always visible */}
            <button
              onClick={toggleMenu}
              className="text-white hover:text-primary-200 focus:outline-none p-2"
              aria-label="Toggle menu"
            >
              <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
          </div>
        </div>

        {/* Navigation Menu - for all screen sizes */}
        {isMenuOpen && (
          <div ref={menuRef} className="bg-primary-700 rounded-b-lg shadow-lg pb-4 animate-slideIn hamburger-menu">
            <div className="flex flex-col space-y-3 pt-2 px-4">
              {user ? (
                <>
                  {!activeWorkout && (
                    <Link
                      to="/active-workout"
                      className="bg-white text-primary-600 px-4 py-2 rounded-md font-medium hover:bg-primary-50 transition-colors text-center mb-3"
                    >
                      <i className="fas fa-plus-circle mr-1"></i> Start Workout
                    </Link>
                  )}

                  <Link
                    to="/statistics"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-chart-line mr-2"></i> Workout Statistics
                  </Link>

                  <Link
                    to="/progress"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-trophy mr-2"></i> Progress
                  </Link>

                  <Link
                    to="/leaderboard"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-medal mr-2"></i> Leaderboard
                  </Link>

                  <Link
                    to="/workout-finder"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-search mr-2"></i> Find Exercises
                  </Link>

                  <Link
                    to="/generate-workout"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-magic mr-2"></i> Generate Workout
                  </Link>

                  <Link
                    to="/workout-planning"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-calendar-alt mr-2"></i> Workout Planning
                  </Link>

                  <Link
                    to="/form-analysis"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-video mr-2"></i> Form Analysis
                  </Link>

                  <Link
                    to="/weight-tracking"
                    className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500"
                  >
                    <i className="fas fa-weight mr-2"></i> Weight Tracking
                  </Link>

                  {/* Only show these on small screens since they're in the top bar on larger screens */}
                  <div className="sm:hidden border-b border-primary-500">
                    <div className="flex items-center justify-between py-2">
                      <span className="text-white">
                        <i className="fas fa-user-circle mr-2"></i> {user.username}
                      </span>
                    </div>

                    <button
                      onClick={handleLogout}
                      className="text-white hover:text-primary-200 transition-colors py-2 text-left w-full"
                    >
                      <i className="fas fa-sign-out-alt mr-2"></i> Logout
                    </button>
                  </div>
                </>
              ) : (
                <>
                  {/* Only show these on small screens since they're in the top bar on larger screens */}
                  <div className="sm:hidden space-y-3">
                    <Link
                      to="/login"
                      className="text-white hover:text-primary-200 transition-colors py-2 border-b border-primary-500 block"
                    >
                      <i className="fas fa-sign-in-alt mr-2"></i> Login
                    </Link>
                    <Link
                      to="/register"
                      className="bg-white text-primary-600 px-4 py-2 rounded-md font-medium hover:bg-primary-50 transition-colors text-center block"
                    >
                      <i className="fas fa-user-plus mr-2"></i> Register
                    </Link>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;