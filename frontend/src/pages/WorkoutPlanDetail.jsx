// frontend/src/pages/WorkoutPlanDetail.jsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { format, isToday, isPast, isFuture } from 'date-fns';
import api from '../services/api';
import PullToRefresh from '../components/PullToRefresh';

const WorkoutPlanDetail = () => {
  const { planId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [workoutPlan, setWorkoutPlan] = useState(null);
  const [plannedWorkouts, setPlannedWorkouts] = useState([]);

  useEffect(() => {
    loadWorkoutPlanDetail();
  }, [planId]);

  const loadWorkoutPlanDetail = async () => {
    try {
      setLoading(true);
      setError('');
      const planDetail = await api.getWorkoutPlanDetail(planId);
      setWorkoutPlan(planDetail);
      setPlannedWorkouts(planDetail.planned_workouts || []);
    } catch (err) {
      console.error('Error loading workout plan detail:', err);
      setError('Failed to load workout plan details');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    await loadWorkoutPlanDetail();
  };

  const handleStartWorkout = async (plannedWorkoutId) => {
    try {
      setError('');
      setSuccess('');
      
      const workout = await api.startWorkoutFromPlan(plannedWorkoutId);
      setSuccess('Workout started successfully!');
      
      // Navigate to active workout
      navigate('/active-workout', { 
        state: { 
          workoutCreated: true, 
          workoutId: workout.id 
        } 
      });
    } catch (err) {
      console.error('Error starting workout from plan:', err);
      setError(err.message || 'Failed to start workout');
    }
  };

  const handleSkipWorkout = async (plannedWorkoutId) => {
    if (!window.confirm('Are you sure you want to skip this workout?')) {
      return;
    }

    try {
      setError('');
      setSuccess('');
      
      await api.skipPlannedWorkout(plannedWorkoutId);
      setSuccess('Workout marked as skipped');
      
      // Reload the plan details
      await loadWorkoutPlanDetail();
    } catch (err) {
      console.error('Error skipping workout:', err);
      setError('Failed to skip workout');
    }
  };

  const getWorkoutStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'skipped': return 'bg-yellow-100 text-yellow-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDateStatus = (plannedDate) => {
    const date = new Date(plannedDate);
    if (isToday(date)) return 'today';
    if (isPast(date)) return 'past';
    if (isFuture(date)) return 'future';
    return 'unknown';
  };

  const getDateStatusColor = (plannedDate) => {
    const status = getDateStatus(plannedDate);
    switch (status) {
      case 'today': return 'border-l-4 border-l-blue-500 bg-blue-50';
      case 'past': return 'border-l-4 border-l-gray-400 bg-gray-50';
      case 'future': return 'border-l-4 border-l-green-500 bg-green-50';
      default: return 'border-l-4 border-l-gray-300';
    }
  };

  const canStartWorkout = (plannedWorkout) => {
    return plannedWorkout.status === 'scheduled' && 
           (isToday(new Date(plannedWorkout.planned_date)) || isPast(new Date(plannedWorkout.planned_date)));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <i className="fas fa-spinner fa-spin text-2xl text-gray-400 mb-2"></i>
          <p className="text-gray-500">Loading workout plan...</p>
        </div>
      </div>
    );
  }

  if (error && !workoutPlan) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
        <button
          onClick={() => navigate('/workout-planning')}
          className="btn btn-secondary mt-4"
        >
          <i className="fas fa-arrow-left mr-2"></i>
          Back to Workout Planning
        </button>
      </div>
    );
  }

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => navigate('/workout-planning')}
              className="btn btn-secondary mr-4"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back
            </button>
            <h1 className="text-2xl sm:text-3xl font-bold text-dark-800">
              {workoutPlan?.name || 'Workout Plan'}
            </h1>
          </div>

          {workoutPlan?.description && (
            <p className="text-gray-600 mb-4">{workoutPlan.description}</p>
          )}

          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            {workoutPlan?.start_date && (
              <span>
                <i className="fas fa-calendar-alt mr-1"></i>
                {format(new Date(workoutPlan.start_date), 'MMM d, yyyy')} - 
                {workoutPlan.end_date && format(new Date(workoutPlan.end_date), 'MMM d, yyyy')}
              </span>
            )}
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getWorkoutStatusColor(workoutPlan?.status)}`}>
              {workoutPlan?.status}
            </span>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            {success}
          </div>
        )}

        {/* Planned Workouts */}
        <div className="card">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Planned Workouts</h2>
            
            {plannedWorkouts.length === 0 ? (
              <div className="text-center py-8">
                <i className="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                <p className="text-gray-500">No planned workouts found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {plannedWorkouts.map((plannedWorkout) => (
                  <div 
                    key={plannedWorkout.id} 
                    className={`border rounded-lg p-4 ${getDateStatusColor(plannedWorkout.planned_date)}`}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-lg font-medium text-dark-800">
                          Day {plannedWorkout.day_of_plan}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {format(new Date(plannedWorkout.planned_date), 'EEEE, MMMM d, yyyy')}
                          {isToday(new Date(plannedWorkout.planned_date)) && (
                            <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium">
                              Today
                            </span>
                          )}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getWorkoutStatusColor(plannedWorkout.status)}`}>
                        {plannedWorkout.status}
                      </span>
                    </div>

                    {/* Exercises */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Planned Exercises:</h4>
                      <div className="grid gap-2">
                        {plannedWorkout.exercises && plannedWorkout.exercises.map((exercise, index) => (
                          <div key={index} className="flex justify-between items-center text-sm bg-white p-2 rounded border">
                            <span className="font-medium">{exercise.name}</span>
                            <span className="text-gray-500">
                              {exercise.reps && `${exercise.reps}`}
                              {exercise.notes && ` - ${exercise.notes}`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-2">
                      {plannedWorkout.status === 'scheduled' && (
                        <>
                          {canStartWorkout(plannedWorkout) ? (
                            <button
                              onClick={() => handleStartWorkout(plannedWorkout.id)}
                              className="btn btn-sm btn-primary"
                            >
                              <i className="fas fa-play mr-1"></i>
                              Start Workout
                            </button>
                          ) : (
                            <span className="text-sm text-gray-500 px-3 py-1">
                              Available {format(new Date(plannedWorkout.planned_date), 'MMM d')}
                            </span>
                          )}
                          <button
                            onClick={() => handleSkipWorkout(plannedWorkout.id)}
                            className="btn btn-sm btn-secondary"
                          >
                            <i className="fas fa-forward mr-1"></i>
                            Skip
                          </button>
                        </>
                      )}
                      
                      {plannedWorkout.status === 'completed' && plannedWorkout.workout_id && (
                        <button
                          onClick={() => navigate(`/workout/${plannedWorkout.workout_id}`)}
                          className="btn btn-sm btn-secondary"
                        >
                          <i className="fas fa-eye mr-1"></i>
                          View Workout
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PullToRefresh>
  );
};

export default WorkoutPlanDetail;
