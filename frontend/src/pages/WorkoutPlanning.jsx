// frontend/src/pages/WorkoutPlanning.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../services/api';
import PullToRefresh from '../components/PullToRefresh';

const WorkoutPlanning = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [loadingPlans, setLoadingPlans] = useState(true);
  const [loadingEquipment, setLoadingEquipment] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state for generating new plan
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [goals, setGoals] = useState('');
  const [durationWeeks, setDurationWeeks] = useState(4);
  const [frequencyPerWeek, setFrequencyPerWeek] = useState(3);
  const [selectedEquipment, setSelectedEquipment] = useState([]);
  const [equipmentList, setEquipmentList] = useState([]);

  // Existing plans state
  const [workoutPlans, setWorkoutPlans] = useState([]);

  // Load data on component mount
  useEffect(() => {
    loadWorkoutPlans();
    loadEquipment();
  }, []);

  const loadWorkoutPlans = async () => {
    try {
      setLoadingPlans(true);
      const plans = await api.getWorkoutPlans();
      setWorkoutPlans(plans);
    } catch (err) {
      console.error('Error loading workout plans:', err);
      setError('Failed to load workout plans');
    } finally {
      setLoadingPlans(false);
    }
  };

  const loadEquipment = async () => {
    try {
      setLoadingEquipment(true);
      const equipment = await api.getEquipment();
      setEquipmentList(equipment);
    } catch (err) {
      console.error('Error loading equipment:', err);
      setError('Failed to load equipment list');
    } finally {
      setLoadingEquipment(false);
    }
  };

  const handleRefresh = async () => {
    await loadWorkoutPlans();
  };

  const handleEquipmentChange = (e) => {
    const value = e.target.value;
    if (e.target.checked) {
      setSelectedEquipment([...selectedEquipment, value]);
    } else {
      setSelectedEquipment(selectedEquipment.filter(item => item !== value));
    }
  };

  const handleGeneratePlan = async (e) => {
    e.preventDefault();
    
    if (!goals.trim()) {
      setError('Please describe your workout goals');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      const response = await api.generateWorkoutPlan({
        goals: goals.trim(),
        duration_weeks: durationWeeks,
        frequency_per_week: frequencyPerWeek,
        equipment: selectedEquipment
      });

      setSuccess('Workout plan generated successfully!');
      setShowGenerateForm(false);
      
      // Reset form
      setGoals('');
      setDurationWeeks(4);
      setFrequencyPerWeek(3);
      setSelectedEquipment([]);
      
      // Reload plans
      await loadWorkoutPlans();

    } catch (err) {
      console.error('Error generating workout plan:', err);
      setError(err.message || 'Failed to generate workout plan');
    } finally {
      setLoading(false);
    }
  };

  const handleViewPlan = (planId) => {
    navigate(`/workout-plans/${planId}`);
  };

  const handleDeletePlan = async (planId) => {
    if (!window.confirm('Are you sure you want to delete this workout plan?')) {
      return;
    }

    try {
      await api.deleteWorkoutPlan(planId);
      setSuccess('Workout plan deleted successfully');
      await loadWorkoutPlans();
    } catch (err) {
      console.error('Error deleting workout plan:', err);
      setError('Failed to delete workout plan');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h1 className="text-2xl sm:text-3xl font-bold text-dark-800 mb-4 sm:mb-0">
              Workout Planning
            </h1>
            <button
              onClick={() => setShowGenerateForm(!showGenerateForm)}
              className="btn btn-primary"
              disabled={loading}
            >
              <i className="fas fa-plus-circle mr-2"></i>
              {showGenerateForm ? 'Cancel' : 'Generate New Plan'}
            </button>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            {success}
          </div>
        )}

        {/* Generate Plan Form */}
        {showGenerateForm && (
          <div className="card mb-8">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">Generate New Workout Plan</h2>
              
              <form onSubmit={handleGeneratePlan} className="space-y-6">
                {/* Goals */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Workout Goals *
                  </label>
                  <textarea
                    value={goals}
                    onChange={(e) => setGoals(e.target.value)}
                    placeholder="e.g., I want to workout each muscle group twice a week for the next month, focusing on strength building..."
                    className="form-textarea w-full h-24"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Describe your goals, preferred muscle groups, workout style, etc.
                  </p>
                </div>

                {/* Duration and Frequency */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration (weeks)
                    </label>
                    <select
                      value={durationWeeks}
                      onChange={(e) => setDurationWeeks(parseInt(e.target.value))}
                      className="form-select w-full"
                    >
                      {[1, 2, 3, 4, 6, 8, 12].map(weeks => (
                        <option key={weeks} value={weeks}>{weeks} week{weeks > 1 ? 's' : ''}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Workouts per week
                    </label>
                    <select
                      value={frequencyPerWeek}
                      onChange={(e) => setFrequencyPerWeek(parseInt(e.target.value))}
                      className="form-select w-full"
                    >
                      {[1, 2, 3, 4, 5, 6, 7].map(freq => (
                        <option key={freq} value={freq}>{freq} workout{freq > 1 ? 's' : ''} per week</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Equipment */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Available Equipment
                  </label>
                  {loadingEquipment ? (
                    <div className="text-gray-500">Loading equipment...</div>
                  ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {equipmentList.map((equipment) => (
                        <label key={equipment.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            value={equipment.name}
                            checked={selectedEquipment.includes(equipment.name)}
                            onChange={handleEquipmentChange}
                            className="form-checkbox"
                          />
                          <span className="text-sm">{equipment.name}</span>
                        </label>
                      ))}
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-1">
                    Select all equipment you have access to. Leave empty for bodyweight-only workouts.
                  </p>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowGenerateForm(false)}
                    className="btn btn-secondary"
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading || !goals.trim()}
                  >
                    {loading ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        Generating...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-magic mr-2"></i>
                        Generate Plan
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Existing Plans */}
        <div className="card">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Your Workout Plans</h2>
            
            {loadingPlans ? (
              <div className="text-center py-8">
                <i className="fas fa-spinner fa-spin text-2xl text-gray-400 mb-2"></i>
                <p className="text-gray-500">Loading workout plans...</p>
              </div>
            ) : workoutPlans.length === 0 ? (
              <div className="text-center py-8">
                <i className="fas fa-calendar-alt text-4xl text-gray-300 mb-4"></i>
                <p className="text-gray-500 mb-4">No workout plans yet</p>
                <button
                  onClick={() => setShowGenerateForm(true)}
                  className="btn btn-primary"
                >
                  Create Your First Plan
                </button>
              </div>
            ) : (
              <div className="grid gap-4">
                {workoutPlans.map((plan) => (
                  <div key={plan.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-medium text-dark-800">{plan.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(plan.status)}`}>
                        {plan.status}
                      </span>
                    </div>
                    
                    {plan.description && (
                      <p className="text-gray-600 text-sm mb-3">{plan.description}</p>
                    )}
                    
                    <div className="flex justify-between items-center text-sm text-gray-500 mb-3">
                      <span>
                        {plan.start_date && format(new Date(plan.start_date), 'MMM d, yyyy')} - 
                        {plan.end_date && format(new Date(plan.end_date), 'MMM d, yyyy')}
                      </span>
                    </div>
                    
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleViewPlan(plan.id)}
                        className="btn btn-sm btn-primary"
                      >
                        <i className="fas fa-eye mr-1"></i>
                        View Details
                      </button>
                      <button
                        onClick={() => handleDeletePlan(plan.id)}
                        className="btn btn-sm btn-danger"
                      >
                        <i className="fas fa-trash mr-1"></i>
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PullToRefresh>
  );
};

export default WorkoutPlanning;
