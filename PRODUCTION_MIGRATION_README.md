# Workout Planning Feature - Production Migration

This directory contains the migration script to upgrade your production database for the new **Workout Planning** feature.

## 🎯 What This Migration Does

The migration adds comprehensive workout planning functionality to your app:

1. **Creates `workout_plans` table** - Stores user-created workout plans
2. **Creates `planned_workouts` table** - Stores individual workouts within plans  
3. **Adds `planned_workout_id` column** - Links completed workouts back to their plans

## 📋 Prerequisites

1. **Python 3.7+** installed on your production server
2. **Database access** to your PostgreSQL instance
3. **Backup your database** before running (recommended)

## 🚀 Migration Steps

### 1. Install Dependencies

```bash
pip install -r migration_requirements.txt
```

### 2. Set Environment Variables

**Option A: Using DATABASE_URL (recommended for production)**
```bash
export DATABASE_URL="postgresql://username:password@host:port/database_name"
```

**Option B: Using individual variables**
```bash
export DB_HOST="your-db-host"
export DB_PORT="5432"
export DB_NAME="workout_db"
export DB_USER="postgres"
export DB_PASSWORD="your-password"
```

### 3. Run Migration

```bash
python workout_planning_migration.py
```

The script will:
- ✅ Check existing database structure
- ✅ Create only missing tables/columns
- ✅ Verify all changes
- ✅ Rollback on any errors

## 🔍 What Gets Created

### `workout_plans` Table
```sql
CREATE TABLE workout_plans (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR NOT NULL,
    description TEXT,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    status VARCHAR DEFAULT 'active',
    raw_text TEXT
);
```

### `planned_workouts` Table  
```sql
CREATE TABLE planned_workouts (
    id SERIAL PRIMARY KEY,
    workout_plan_id INTEGER REFERENCES workout_plans(id),
    workout_id INTEGER REFERENCES workouts(id),
    planned_date TIMESTAMP NOT NULL,
    status VARCHAR DEFAULT 'scheduled',
    day_of_plan INTEGER NOT NULL,
    exercises JSON NOT NULL
);
```

### `workouts` Table Update
```sql
ALTER TABLE workouts 
ADD COLUMN planned_workout_id INTEGER 
REFERENCES planned_workouts(id);
```

## ✅ Verification

The script automatically verifies:
- All tables exist
- All columns exist  
- Foreign key relationships are correct

## 🔄 After Migration

1. **Deploy new code** with workout planning features
2. **Test the feature** in production
3. **Users can now**:
   - Generate AI-powered workout plans
   - Schedule workouts for specific dates
   - Track completion automatically

## 🆘 Troubleshooting

### Connection Issues
- Verify database credentials
- Check firewall/security group settings
- Ensure PostgreSQL is running

### Permission Issues
- Ensure database user has CREATE TABLE permissions
- Check if user can ALTER existing tables

### Migration Already Run
- Script is idempotent - safe to run multiple times
- Will skip existing tables/columns

## 📞 Support

If you encounter issues:
1. Check the migration logs for specific error messages
2. Verify database permissions
3. Ensure all prerequisites are met

## 🔙 Rollback (if needed)

If you need to rollback the migration:

```sql
-- Remove the new column
ALTER TABLE workouts DROP COLUMN IF EXISTS planned_workout_id;

-- Drop the new tables (this will delete all workout planning data)
DROP TABLE IF EXISTS planned_workouts CASCADE;
DROP TABLE IF EXISTS workout_plans CASCADE;
```

⚠️ **Warning**: Rollback will permanently delete all workout planning data!
