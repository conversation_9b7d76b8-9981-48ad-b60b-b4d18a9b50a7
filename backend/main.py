from fastapi import <PERSON>AP<PERSON>, Depends, HTTPException, status, Request, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel, Field, validator
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from enum import Enum
from typing import List, Optional
import os
import logging
import re
from fastapi import Request
from fastapi.responses import JSONResponse
from sqlalchemy import func
from sqlalchemy.orm import joinedload

# Import local modules
import models
from database import engine, get_db
from gemini_service import GeminiService
from form_analysis_service import FormAnalysisService
from scoring_service import ScoringService
from utils.exercise_matcher import find_matching_exercise, normalize_exercise_name
from utils.timezone_utils import get_eastern_now

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create tables in the database
models.Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Workout Tracker API",
    description="A comprehensive workout tracking application",
    version="1.0.0"
)

# Define Pydantic models for the scoring system
class UserStats(BaseModel):
    total_score: int
    total_workouts: int
    current_streak: int
    longest_streak: int

    class Config:
        from_attributes = True

class Milestone(BaseModel):
    id: int
    name: str
    description: str
    target_value: int
    current_value: int
    completed: bool

    class Config:
        from_attributes = True

class Achievement(BaseModel):
    id: int
    name: str
    description: str
    icon: str

    class Config:
        from_attributes = True

class UserAchievement(BaseModel):
    id: int
    achievement_id: int
    date_earned: datetime
    achievement: Achievement

    class Config:
        from_attributes = True

class LeaderboardEntry(BaseModel):
    username: str
    total_score: int
    total_workouts: int
    current_streak: int
    longest_streak: int
    achievement_count: int

    class Config:
        from_attributes = True

# Initialize Services
gemini_service = GeminiService()
form_analysis_service = FormAnalysisService()

# Initialize ScoringService with a dependency
def get_scoring_service(db: Session = Depends(get_db)):
    return ScoringService(db)

# Configure CORS
origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://frontend:3000",
    "https://quadsquad.techarz.site",
    "*"  # Caution: use specific origins in production
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your_secret_key_here_change_in_production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 240  # 4 hours

# Security Utilities
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Define Exercise Set Models
class ExerciseSetBase(BaseModel):
    reps: Optional[int] = None
    weight: Optional[float] = None
    duration_seconds: Optional[int] = None
    distance: Optional[float] = None
    notes: Optional[str] = None

class ExerciseSetCreate(ExerciseSetBase):
    pass

class ExerciseSet(ExerciseSetBase):
    id: int
    exercise_id: int

    class Config:
        from_attributes = True

# Define Exercise Types
class ExerciseTypeEnum(str, Enum):
    strength = "strength"
    cardio = "cardio"
    flexibility = "flexibility"
    other = "other"

# Define Exercise Models
class ExerciseBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    exercise_type: ExerciseTypeEnum = ExerciseTypeEnum.strength

class ExerciseCreate(ExerciseBase):
    sets: List[ExerciseSetCreate] = []
    from_llm: bool = False  # Flag to indicate if this exercise was generated by LLM

class Exercise(ExerciseBase):
    id: int
    workout_id: int
    sets: List[ExerciseSet] = []

    class Config:
        from_attributes = True
class WorkoutSuggestionRequest(BaseModel):
    muscleGroup: str
    equipment: Optional[List[str]] = []
    goals: Optional[str] = None

class WorkoutGenerationRequest(BaseModel):
    equipment: List[str] = []
    exercise_count: int = Field(ge=1, le=10, description="Number of exercises to generate (1-10)")
    goals: str = Field(..., min_length=1, description="User's workout goals")

class ExerciseSuggestion(BaseModel):
    name: str
    type: Optional[str] = "strength"
    equipment: Optional[str] = None
    description: Optional[str] = None
    instructions: Optional[List[str]] = None
    sets: Optional[int] = None
    reps: Optional[str] = None
    from_llm: Optional[bool] = True  # Default to True since these are typically from LLM

class WorkoutSuggestionResponse(BaseModel):
    exercises: List[ExerciseSuggestion]
    generalAdvice: Optional[str] = None

class WorkoutGenerationResponse(BaseModel):
    exercises: List[ExerciseSuggestion]
    generalAdvice: Optional[str] = None

# Define Workout Status
class WorkoutStatusEnum(str, Enum):
    in_progress = "in_progress"
    completed = "completed"

# User Pydantic Models
class UserBase(BaseModel):
    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="Username must be between 3 and 50 characters"
    )

    @validator('username')
    def validate_username(cls, v):
        # Ensure username is alphanumeric
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('Username must be alphanumeric')
        return v

class UserCreate(UserBase):
    password: str = Field(
        ...,
        min_length=8,
        max_length=100,
        description="Password must be at least 8 characters"
    )

    @validator('password')
    def validate_password(cls, v):
        # Ensure password meets complexity requirements
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one number')
        return v

class User(UserBase):
    id: int
    is_active: bool

    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# Workout Models
class WorkoutBase(BaseModel):
    date: datetime = Field(default_factory=get_eastern_now)
    description: Optional[str] = Field(None, max_length=500, description="Workout description between 1 and 500 characters")
    status: WorkoutStatusEnum = WorkoutStatusEnum.completed

class WorkoutCreate(WorkoutBase):
    exercises: List[ExerciseCreate] = []

class Workout(WorkoutBase):
    id: int
    user_id: int
    exercises: List[Exercise] = []

    class Config:
        from_attributes = True

class SocialWorkout(Workout):
    username: str

# Statistics Models
class WorkoutStatistics(BaseModel):
    total_workouts: int
    total_days_active: int
    most_frequent_workout_type: Optional[str]
    average_workout_length: Optional[float]

class PersonalRecord(BaseModel):
    exercise_name: str
    exercise_type: str
    value: float
    unit: str
    date: datetime
    workout_id: int

# Scoring Models
class WorkoutScoreBase(BaseModel):
    base_score: int
    streak_bonus: int
    variety_bonus: int
    new_exercise_bonus: int
    total_score: int
    details: Optional[Dict[str, Any]] = None

class WorkoutScoreCreate(WorkoutScoreBase):
    workout_id: int
    user_id: int

class WorkoutScore(WorkoutScoreBase):
    id: int
    workout_id: int
    user_id: int
    date: datetime

    class Config:
        from_attributes = True

# Milestone Models
class MilestoneBase(BaseModel):
    title: str
    description: Optional[str] = None
    milestone_type: str
    target_value: int
    current_value: int
    status: str

class MilestoneCreate(MilestoneBase):
    user_id: int

class Milestone(MilestoneBase):
    id: int
    user_id: int
    start_date: datetime
    end_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    points_awarded: int

    class Config:
        from_attributes = True

# Achievement Models
class AchievementBase(BaseModel):
    name: str
    description: Optional[str] = None
    icon: Optional[str] = None
    points: int

class Achievement(AchievementBase):
    id: int

    class Config:
        from_attributes = True

class UserAchievement(BaseModel):
    id: int
    user_id: int
    achievement_id: int
    date_earned: datetime
    achievement: Achievement

    class Config:
        from_attributes = True

# User Stats Models
class UserStatsBase(BaseModel):
    total_score: int
    current_streak: int
    longest_streak: int
    total_workouts: int
    unique_exercises: int

class UserStats(UserStatsBase):
    id: int
    user_id: int
    last_updated: datetime

    class Config:
        from_attributes = True

# User Weight Models
class UserWeightBase(BaseModel):
    weight: float
    body_fat: Optional[float] = None
    date: datetime = Field(default_factory=get_eastern_now)
    notes: Optional[str] = None

class UserWeightCreate(UserWeightBase):
    pass

class UserWeight(UserWeightBase):
    id: int
    user_id: int

    class Config:
        from_attributes = True

# Define Workout Analysis Model
class WorkoutAnalysis(BaseModel):
    """Response model for workout analysis"""
    summary: str
    progress: str
    insights: str
    recommendations: str
    raw_response: Optional[str] = None
    processed_data: Optional[Dict[str, Any]] = None

# Define Form Analysis Models
class FormIssueResponse(BaseModel):
    """Response model for form issues"""
    issue_type: str
    severity: float
    frame_number: Optional[int] = None
    description: str
    recommendation: Optional[str] = None

class FormAnalysisResponse(BaseModel):
    """Response model for form analysis"""
    id: int
    overall_score: float
    feedback: str
    recommendations: str
    issues: List[FormIssueResponse] = []
    analysis_data: Optional[Dict[str, Any]] = None

class FormAnalysisRequest(BaseModel):
    """Request model for form analysis"""
    exercise_id: int

# Workout Planning Models
class WorkoutPlanGenerationRequest(BaseModel):
    """Request model for generating a workout plan"""
    goals: str = Field(..., min_length=1, description="User's workout goals and requirements")
    duration_weeks: int = Field(default=4, ge=1, le=12, description="Duration of the plan in weeks")
    equipment: List[str] = Field(default=[], description="Available equipment")
    frequency_per_week: int = Field(default=3, ge=1, le=7, description="Workouts per week")

class PlannedExercise(BaseModel):
    """Model for a planned exercise within a planned workout"""
    name: str
    exercise_type: str
    sets: Optional[int] = None
    reps: Optional[str] = None
    weight: Optional[str] = None
    duration: Optional[str] = None
    notes: Optional[str] = None

class WorkoutPlanBase(BaseModel):
    """Base model for workout plans"""
    name: str
    description: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    status: str = "active"

class WorkoutPlanCreate(WorkoutPlanBase):
    """Model for creating a workout plan"""
    pass

class WorkoutPlan(WorkoutPlanBase):
    """Response model for workout plans"""
    id: int
    user_id: int
    raw_text: Optional[str] = None

    class Config:
        from_attributes = True

class PlannedWorkoutBase(BaseModel):
    """Base model for planned workouts"""
    planned_date: datetime
    day_of_plan: int
    exercises: List[PlannedExercise]
    status: str = "scheduled"

class PlannedWorkoutCreate(PlannedWorkoutBase):
    """Model for creating a planned workout"""
    workout_plan_id: int

class PlannedWorkout(PlannedWorkoutBase):
    """Response model for planned workouts"""
    id: int
    workout_plan_id: int
    workout_id: Optional[int] = None

    class Config:
        from_attributes = True

class WorkoutPlanDetail(WorkoutPlan):
    """Detailed workout plan with all planned workouts"""
    planned_workouts: List[PlannedWorkout] = []

class WorkoutPlanGenerationResponse(BaseModel):
    """Response model for workout plan generation"""
    workout_plan: WorkoutPlan
    planned_workouts: List[PlannedWorkout]
    raw_llm_response: Optional[str] = None

# Authentication Utility Functions
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against a hashed password."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate a hash for a given password."""
    return pwd_context.hash(password)

def authenticate_user(db: Session, username: str, password: str):
    """Authenticate a user based on username and password."""
    user = db.query(models.User).filter(models.User.username == username).first()
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()

    # Use consistent Eastern timezone timestamp
    if expires_delta:
        expire = get_eastern_now() + expires_delta
    else:
        expire = get_eastern_now() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    # Use integer timestamp for JWT
    to_encode.update({"exp": int(expire.timestamp())})

    # Optional: add issued at time
    to_encode.update({"iat": int(get_eastern_now().timestamp())})

    # Log token creation (be careful in production)
    logger.info(f"Creating token for user: {data.get('sub')}")

    # Encode token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """Get the current user from a JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Decode the token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # Extract username
        username: str = payload.get("sub")
        if username is None:
            logger.warning("No username in token payload")
            raise credentials_exception

        # Validate token data
        token_data = TokenData(username=username)
    except JWTError as e:
        # Log specific JWT decoding error
        logger.error(f"JWT Error during token validation: {e}")
        raise credentials_exception

    # Find user in database
    user = db.query(models.User).filter(models.User.username == token_data.username).first()
    if user is None:
        logger.warning(f"No user found for username: {token_data.username}")
        raise credentials_exception

    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
):
    """Ensure the current user is active."""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Global Exception Handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

# Authentication Endpoints
@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Endpoint for user login and token generation."""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username},
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer"
    }

@app.post("/users/", response_model=User)
def create_user(user: UserCreate, db: Session = Depends(get_db)):
    """Endpoint for user registration."""
    # Check if username already exists
    db_user = db.query(models.User).filter(models.User.username == user.username).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Username already registered")

    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        hashed_password=hashed_password,
        is_active=True
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@app.get("/users/me/", response_model=User)
async def read_users_me(
    current_user: User = Depends(get_current_active_user)
):
    """Endpoint to get current user information."""
    return current_user

# Workout Endpoints
@app.post("/workouts/", response_model=Workout)
def create_workout(
    workout: WorkoutCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to create a new workout."""
    db_workout = models.Workout(
        date=workout.date,
        description=workout.description,
        status=workout.status,
        user_id=current_user.id
    )
    db.add(db_workout)
    db.commit()
    db.refresh(db_workout)

    # Add exercises if provided
    for exercise_data in workout.exercises:
        db_exercise = models.Exercise(
            name=exercise_data.name,
            exercise_type=exercise_data.exercise_type,
            workout_id=db_workout.id
        )
        db.add(db_exercise)
        db.flush()

        # Add sets to the exercise
        for set_data in exercise_data.sets:
            db_set = models.ExerciseSet(
                reps=set_data.reps,
                weight=set_data.weight,
                duration_seconds=set_data.duration_seconds,
                distance=set_data.distance,
                notes=set_data.notes,
                exercise_id=db_exercise.id
            )
            db.add(db_set)

    db.commit()
    db.refresh(db_workout)
    return db_workout

@app.get("/workouts/", response_model=List[Workout])
def read_workouts(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to retrieve user's workouts."""
    workouts = db.query(models.Workout).filter(
        models.Workout.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    return workouts

@app.get("/workouts/{workout_id}", response_model=Workout)
def read_workout(
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to retrieve a specific workout."""
    workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()
    if workout is None:
        raise HTTPException(status_code=404, detail="Workout not found")
    return workout

@app.get("/workouts/social/{workout_id}", response_model=SocialWorkout)
def read_social_workout(
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to retrieve any completed workout for the social feed."""
    # Join with User to get the username
    workout_query = (
        db.query(models.Workout, models.User.username)
        .join(models.User)
        .filter(
            models.Workout.id == workout_id,
            models.Workout.status == models.WorkoutStatus.completed
        )
    )

    result = workout_query.first()

    if result is None:
        raise HTTPException(status_code=404, detail="Workout not found")

    workout, username = result

    # Create a dictionary with all workout attributes
    workout_dict = {
        "id": workout.id,
        "user_id": workout.user_id,
        "date": workout.date,
        "description": workout.description,
        "status": workout.status,
        "exercises": workout.exercises,
        "username": username
    }

    return SocialWorkout(**workout_dict)

@app.put("/workouts/{workout_id}", response_model=Workout)
def update_workout(
    workout_id: int,
    workout: WorkoutCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to update a specific workout."""
    db_workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()

    if db_workout is None:
        raise HTTPException(status_code=404, detail="Workout not found")

    db_workout.date = workout.date
    db_workout.description = workout.description
    db_workout.status = workout.status

    db.commit()
    db.refresh(db_workout)
    return db_workout

@app.delete("/workouts/{workout_id}")
def delete_workout(
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to delete a specific workout."""
    db_workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()

    if db_workout is None:
        raise HTTPException(status_code=404, detail="Workout not found")

    db.delete(db_workout)
    db.commit()
    return {"detail": "Workout deleted successfully"}

# New Workout Endpoints
@app.post("/workouts/start/", response_model=Workout)
def start_workout(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Start a new workout in progress."""
    db_workout = models.Workout(
        date=get_eastern_now(),
        status=models.WorkoutStatus.in_progress,
        user_id=current_user.id
    )
    db.add(db_workout)
    db.commit()
    db.refresh(db_workout)
    return db_workout

@app.put("/workouts/{workout_id}/complete", response_model=Workout)
def complete_workout(
    workout_id: int,
    description: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    scoring_service: ScoringService = Depends(get_scoring_service)
):
    """Mark a workout as completed and calculate score."""
    db_workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()

    if db_workout is None:
        raise HTTPException(status_code=404, detail="Workout not found")

    db_workout.status = models.WorkoutStatus.completed
    if description:
        db_workout.description = description

    # If this workout was started from a planned workout, mark the planned workout as completed
    if db_workout.planned_workout_id:
        planned_workout = db.query(models.PlannedWorkout).filter(
            models.PlannedWorkout.id == db_workout.planned_workout_id
        ).first()
        if planned_workout:
            planned_workout.status = models.PlannedWorkoutStatus.completed
            planned_workout.workout_id = workout_id
            logger.info(f"Automatically marked planned workout {planned_workout.id} as completed")

    db.commit()
    db.refresh(db_workout)

    # Calculate workout score
    try:
        score_result = scoring_service.calculate_workout_score(workout_id)
        logger.info(f"Workout {workout_id} scored: {score_result['total_score']} points")
    except Exception as e:
        logger.error(f"Error calculating workout score: {str(e)}")

    return db_workout

@app.delete("/workouts/{workout_id}/discard")
def discard_workout(
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Discard an in-progress workout."""
    db_workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id,
        models.Workout.status == models.WorkoutStatus.in_progress
    ).first()

    if db_workout is None:
        raise HTTPException(status_code=404, detail="In-progress workout not found")

    # Delete the workout
    db.delete(db_workout)
    db.commit()

    return {"detail": "Workout discarded successfully"}

@app.get("/workouts/in-progress/", response_model=Optional[Workout])
def get_workout_in_progress(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get the current user's workout in progress, if any."""
    db_workout = db.query(models.Workout).filter(
        models.Workout.user_id == current_user.id,
        models.Workout.status == models.WorkoutStatus.in_progress
    ).first()

    return db_workout

# Scoring System Endpoints
@app.get("/workouts/{workout_id}/score", response_model=WorkoutScore)
def get_workout_score(
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    scoring_service: ScoringService = Depends(get_scoring_service)
):
    """Get the score for a specific workout."""
    # Verify workout exists and belongs to user
    workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()

    if not workout:
        raise HTTPException(status_code=404, detail="Workout not found")

    # Get workout score
    score = db.query(models.WorkoutScore).filter(
        models.WorkoutScore.workout_id == workout_id
    ).first()

    if not score:
        # If score doesn't exist and workout is completed, calculate it
        if workout.status == models.WorkoutStatus.completed:
            try:
                score_result = scoring_service.calculate_workout_score(workout_id)
                # Fetch the newly created score
                score = db.query(models.WorkoutScore).filter(
                    models.WorkoutScore.workout_id == workout_id
                ).first()
            except Exception as e:
                logger.error(f"Error calculating workout score: {str(e)}")
                raise HTTPException(status_code=500, detail="Error calculating workout score")
        else:
            raise HTTPException(status_code=400, detail="Workout is not completed, no score available")

    if not score:
        raise HTTPException(status_code=404, detail="Score not found")

    return score

@app.get("/users/me/stats", response_model=UserStats)
def get_user_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    scoring_service: ScoringService = Depends(get_scoring_service)
):
    """Get the current user's statistics."""
    # Get user stats
    user_stats = db.query(models.UserStat).filter(
        models.UserStat.user_id == current_user.id
    ).first()

    if not user_stats:
        # Create user stats if they don't exist
        user_stats = scoring_service._get_or_create_user_stats(current_user.id)
        if not user_stats:
            raise HTTPException(status_code=500, detail="Error creating user stats")

    return user_stats

@app.get("/users/me/milestones", response_model=List[Milestone])
def get_user_milestones(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    scoring_service: ScoringService = Depends(get_scoring_service)
):
    """Get the current user's milestones."""
    # Check if user has milestones
    milestones = db.query(models.Milestone).filter(
        models.Milestone.user_id == current_user.id
    ).all()

    if not milestones:
        # Create default milestones if they don't exist
        scoring_service.create_default_milestones(current_user.id)
        milestones = db.query(models.Milestone).filter(
            models.Milestone.user_id == current_user.id
        ).all()

    return milestones

@app.get("/users/me/achievements", response_model=List[UserAchievement])
def get_user_achievements(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    scoring_service: ScoringService = Depends(get_scoring_service)
):
    """Get the current user's achievements."""
    # Check if achievements exist
    achievements_count = db.query(models.Achievement).count()
    if achievements_count == 0:
        # Create default achievements if they don't exist
        scoring_service.create_default_achievements()

    # Get user achievements
    user_achievements = db.query(models.UserAchievement).filter(
        models.UserAchievement.user_id == current_user.id
    ).options(
        # Load the achievement relationship
        joinedload(models.UserAchievement.achievement)
    ).all()

    return user_achievements

@app.get("/achievements", response_model=List[Achievement])
def get_all_achievements(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    scoring_service: ScoringService = Depends(get_scoring_service)
):
    """Get all available achievements."""
    # Check if achievements exist
    achievements_count = db.query(models.Achievement).count()
    if achievements_count == 0:
        # Create default achievements if they don't exist
        scoring_service.create_default_achievements()

    # Get all achievements
    achievements = db.query(models.Achievement).all()

    return achievements

# Leaderboard Models
class LeaderboardEntry(BaseModel):
    username: str
    total_score: int
    total_workouts: int
    current_streak: int
    longest_streak: int
    unique_exercises: int
    achievement_count: int

@app.get("/leaderboard", response_model=List[LeaderboardEntry])
def get_leaderboard(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get the leaderboard of top users by score."""
    try:
        # Get all users with their stats
        leaderboard_query = (
            db.query(
                models.User.username,
                models.UserStat.total_score,
                models.UserStat.total_workouts,
                models.UserStat.current_streak,
                models.UserStat.longest_streak,
                models.UserStat.unique_exercises,
                func.count(models.UserAchievement.id).label("achievement_count")
            )
            .join(models.UserStat, models.User.id == models.UserStat.user_id)
            .outerjoin(models.UserAchievement, models.User.id == models.UserAchievement.user_id)
            .group_by(
                models.User.username,
                models.UserStat.total_score,
                models.UserStat.total_workouts,
                models.UserStat.current_streak,
                models.UserStat.longest_streak,
                models.UserStat.unique_exercises
            )
            .order_by(models.UserStat.total_score.desc())
        )

        leaderboard = []
        for entry in leaderboard_query:
            leaderboard.append(
                LeaderboardEntry(
                username=entry.username,
                total_score=entry.total_score,
                total_workouts=entry.total_workouts,
                current_streak=entry.current_streak,
                longest_streak=entry.longest_streak,
                unique_exercises=entry.unique_exercises,
                achievement_count=entry.achievement_count
            )
        )

        return leaderboard
    except Exception as e:
        logger.error(f"Error retrieving leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving leaderboard: {str(e)}")

# Updated analysis route in backend/main.py
@app.get("/workouts/{workout_id}/analysis", response_model=WorkoutAnalysis)
async def analyze_workout(
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Generate analysis for a workout, comparing with historical data
    """
    logger.critical(f"🔍 WORKOUT ANALYSIS ENDPOINT CALLED")
    logger.critical(f"🏋️ Workout ID: {workout_id}")
    logger.critical(f"👤 User: {current_user.username}")

    try:
        # Verify workout exists and belongs to user
        workout = db.query(models.Workout).filter(
            models.Workout.id == workout_id,
            models.Workout.user_id == current_user.id
        ).first()

        if not workout:
            logger.error(f"❌ Workout {workout_id} not found for user {current_user.username}")
            raise HTTPException(status_code=404, detail="Workout not found")

        logger.info(f"✅ Workout {workout_id} found for user {current_user.username}")
        logger.info(f"💡 Workout Status: {workout.status}")

        # Verify workout is completed
        if workout.status != models.WorkoutStatus.completed:
            logger.warning(f"⚠️ Attempt to analyze non-completed workout {workout_id}")
            raise HTTPException(status_code=400, detail="Workout must be completed for analysis")

        # Fetch ALL historical workouts except this one
        historical_workouts = db.query(models.Workout).filter(
            models.Workout.user_id == current_user.id,
            models.Workout.id != workout_id,
            models.Workout.status == models.WorkoutStatus.completed
        ).all()

        # Log historical workout details
        logger.info(f"📚 Historical Workouts Found: {len(historical_workouts)}")

        # Prepare workout dictionaries
        workout_dict = {
            "id": workout.id,
            "date": workout.date.isoformat(),
            "description": workout.description,
            "status": workout.status,
            "exercises": [
                {
                    "id": exercise.id,
                    "name": exercise.name,
                    "exercise_type": exercise.exercise_type,
                    "sets": [
                        {
                            "id": set.id,
                            "reps": set.reps,
                            "weight": set.weight,
                            "duration_seconds": set.duration_seconds,
                            "distance": set.distance,
                            "notes": set.notes
                        } for set in exercise.sets
                    ]
                } for exercise in workout.exercises
            ]
        }

        historical_workouts_dicts = [
            {
                "id": hw.id,
                "date": hw.date.isoformat(),
                "status": hw.status,
                "exercises": [
                    {
                        "id": exercise.id,
                        "name": exercise.name,
                        "exercise_type": exercise.exercise_type,
                        "sets": [
                            {
                                "id": set.id,
                                "reps": set.reps,
                                "weight": set.weight,
                                "duration_seconds": set.duration_seconds,
                                "distance": set.distance,
                                "notes": set.notes
                            } for set in exercise.sets
                        ]
                    } for exercise in hw.exercises
                ]
            } for hw in historical_workouts
        ]

        # User context information with more details
        user_info = {
            "username": current_user.username,
            "total_workouts": len(db.query(models.Workout).filter(
                models.Workout.user_id == current_user.id,
                models.Workout.status == models.WorkoutStatus.completed
            ).all()),
            "first_workout_date": min([hw.date for hw in historical_workouts]).isoformat() if historical_workouts else None,
            "average_workouts_per_week": len(historical_workouts) / 4 if historical_workouts else 0  # Simplified calculation
        }

        # Call analysis service
        analysis = await gemini_service.analyze_workout(
            workout_dict,
            historical_workouts_dicts,
            user_info
        )

        if analysis:
            logger.info("✅ Analysis Generated Successfully")
            logger.debug(f"Analysis Details: {analysis}")
            return WorkoutAnalysis(**analysis)
        else:
            logger.error("❌ No analysis generated")
            raise HTTPException(
                status_code=500,
                detail="Could not generate workout analysis"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.critical(f"🚨 UNEXPECTED ERROR: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during workout analysis"
        )

@app.post("/workouts/suggestions/", response_model=WorkoutSuggestionResponse)
async def get_workout_suggestions(
    request: WorkoutSuggestionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate workout suggestions for specific muscle groups using Gemini"""
    try:
        logger.info(f"Workout suggestion request for muscle group: {request.muscleGroup}")

        # Import the exercise list utility
        from utils.exercise_list import get_exercises_by_equipment, format_exercises_for_llm

        # Get exercises that can be performed with the specified equipment
        available_exercises = get_exercises_by_equipment(db, request.equipment)

        # Filter exercises by muscle group if possible
        muscle_group_exercises = []
        for exercise in available_exercises:
            for muscle_group in exercise.muscle_groups:
                if request.muscleGroup.lower() in muscle_group.name.lower():
                    muscle_group_exercises.append(exercise)
                    break

        # If no exercises found for the specific muscle group, use all available exercises
        if not muscle_group_exercises:
            muscle_group_exercises = available_exercises

        # Format the exercises for the LLM
        exercise_list = format_exercises_for_llm(muscle_group_exercises)

        # Build prompt for Gemini
        prompt = f"""
    As an expert personal trainer, provide workout suggestions for a user who wants to focus on their {request.muscleGroup} muscles.

    Additional context:
    - Available equipment: {', '.join(request.equipment) if request.equipment else 'Bodyweight only'}
    - User goals: {request.goals if request.goals else 'General fitness and strength'}

    AVAILABLE EXERCISES:
{exercise_list}

    Please select 3-5 exercises from the AVAILABLE EXERCISES list above that target the {request.muscleGroup} muscles.
    Use the exact exercise names as they appear in the list.

    Provide your suggestions using the following format for each exercise:

    EXERCISE: [Name - EXACTLY as it appears in the AVAILABLE EXERCISES list]
    TYPE: [Type]
    EQUIPMENT: [Required equipment]
    DESCRIPTION: [Brief description]
    INSTRUCTIONS:
    1. [Step 1]
    2. [Step 2]
    3. [Step 3]
    RECOMMENDATION: [Sets and reps]

    Also include a brief paragraph of general advice for training this muscle group effectively.

    IMPORTANT:
    - Do not use markdown formatting like asterisks or pound signs in your response
    - Do not duplicate instruction numbers
    - ONLY use exercise names that appear EXACTLY in the AVAILABLE EXERCISES list
    """

        # Call Gemini API using the same model as the workout analysis
        generation_config = {
            "temperature": 0.2,
            "top_p": 0.95,
            "max_output_tokens": 1024,
        }

        response = gemini_service.model.generate_content(
            prompt,
            generation_config=generation_config
        )

        # Process the response
        if response and hasattr(response, 'text'):
            generated_text = response.text

            logger.info("✅ Workout suggestions generated successfully")

            # Parse the generated text - this is simplified and might need refinement
            # A more robust approach would be to ask Gemini to return structured JSON
            exercises = []
            general_advice = ""

            # Very basic parsing - in production you'd want more robust parsing
            # or have Gemini return structured data directly
            sections = generated_text.split("\n\n")

            for section in sections:
                if "exercise" in section.lower() or ":" in section:
                    # This looks like an exercise
                    lines = section.strip().split("\n")
                    if len(lines) < 3:
                        continue

                    exercise = {}

                    # Extract name from first line
                    exercise["name"] = lines[0].split(":", 1)[-1].strip() if ":" in lines[0] else lines[0].strip()

                    # Process remaining lines
                    instructions = []
                    for line in lines[1:]:
                        if "type:" in line.lower() or "exercise type:" in line.lower():
                            exercise["type"] = line.split(":", 1)[-1].strip()
                        elif "equipment:" in line.lower() or "required equipment:" in line.lower():
                            exercise["equipment"] = line.split(":", 1)[-1].strip()
                        elif "description:" in line.lower():
                            exercise["description"] = line.split(":", 1)[-1].strip()
                        elif "instruction" in line.lower() and ":" in line:
                            continue  # Skip the header
                        elif "set" in line.lower() and "rep" in line.lower():
                            parts = line.split(":", 1)
                            if len(parts) > 1:
                                rec = parts[1].strip()
                                if "set" in rec.lower():
                                    try:
                                        sets = int(rec.split("sets")[0].strip().split()[-1])
                                        exercise["sets"] = sets
                                    except:
                                        pass

                                    if "rep" in rec.lower():
                                        try:
                                            reps = rec.split("reps")[0].split("sets")[-1].strip()
                                            exercise["reps"] = reps
                                        except:
                                            pass
                        elif line.strip() and not any(k in line.lower() for k in ["exercise", "name:"]):
                            # Assume it's an instruction step
                            if line.strip().startswith(("- ", "• ", "* ")):
                                instructions.append(line.strip()[2:])
                            elif line.strip()[0].isdigit() and len(line) > 2 and line[1] in [".", ")"]:
                                instructions.append(line.strip()[2:].strip())
                            else:
                                instructions.append(line.strip())

                    if instructions:
                        exercise["instructions"] = instructions

                    if exercise.get("name"):  # Only add if we have at least a name
                        exercises.append(ExerciseSuggestion(**exercise))

                elif "advice" in section.lower() or "tip" in section.lower():
                    # This looks like general advice
                    general_advice = section.strip()

            # Return default exercise if parsing fails
            if not exercises:
                exercises = [
                    ExerciseSuggestion(
                        name="Exercise suggestion failed",
                        description="Please try again with different parameters."
                    )
                ]

            return WorkoutSuggestionResponse(
                exercises=exercises,
                generalAdvice=general_advice
            )

        else:
            logger.warning("❌ No text response from Gemini API for workout suggestions")
            raise HTTPException(status_code=500, detail="Failed to generate workout suggestions")

    except Exception as e:
        logger.error(f"🔥 ERROR generating workout suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error generating workout suggestions")

@app.post("/workouts/generate/", response_model=WorkoutGenerationResponse)
async def generate_workout(
    request: WorkoutGenerationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate a complete workout based on user's equipment and goals"""
    try:
        logger.info(f"Workout generation request with goals: {request.goals}")
        logger.info(f"Equipment: {', '.join(request.equipment)}")
        logger.info(f"Exercise count: {request.exercise_count}")

        # Import the exercise list utility
        from utils.exercise_list import get_exercises_by_equipment, format_exercises_for_llm

        # Get exercises that can be performed with the specified equipment
        available_exercises = get_exercises_by_equipment(db, request.equipment)

        # Format the exercises for the LLM
        exercise_list = format_exercises_for_llm(available_exercises)

        # Build prompt for Gemini
        prompt = f"""
    As an expert personal trainer, create a complete workout plan for a user with the following parameters:

    EQUIPMENT AVAILABLE: {', '.join(request.equipment) if request.equipment else 'Bodyweight only'}
    EXERCISE COUNT: {request.exercise_count}
    USER GOALS: {request.goals}

    AVAILABLE EXERCISES:
{exercise_list}

    Please select exactly {request.exercise_count} exercises from the AVAILABLE EXERCISES list above.
    Use the exact exercise names as they appear in the list.

    Provide your workout plan using the following format for each exercise:

    EXERCISE: [Name - EXACTLY as it appears in the AVAILABLE EXERCISES list]
    TYPE: [Type - one of: strength, cardio, flexibility, other]
    EQUIPMENT: [Required equipment]
    DESCRIPTION: [Brief description]
    INSTRUCTIONS:
    1. [Step 1]
    2. [Step 2]
    3. [Step 3]
    RECOMMENDATION: [Sets and reps]

    Also include a brief paragraph of general advice for this workout plan.

    IMPORTANT:
    - Do not use markdown formatting like asterisks or pound signs in your response
    - Do not duplicate instruction numbers
    - Provide exactly {request.exercise_count} exercises, no more and no less
    - Make sure the exercises work well together as a complete workout
    - Consider the user's specific goals: {request.goals}
    - ONLY use exercise names that appear EXACTLY in the AVAILABLE EXERCISES list
    """

        # Call Gemini API using the same model as the workout analysis
        generation_config = {
            "temperature": 0.3,  # Slightly higher temperature for more variety
            "top_p": 0.95,
            "max_output_tokens": 2048,  # Higher token limit for more detailed workout
        }

        response = gemini_service.model.generate_content(
            prompt,
            generation_config=generation_config
        )

        # Process the response
        if response and hasattr(response, 'text'):
            generated_text = response.text

            logger.info("✅ Workout plan generated successfully")

            # Parse the generated text - this is simplified and might need refinement
            exercises = []
            general_advice = ""

            # Very basic parsing - in production you'd want more robust parsing
            # or have Gemini return structured data directly
            sections = generated_text.split("\n\n")

            for section in sections:
                if "exercise" in section.lower() or ":" in section:
                    # This looks like an exercise
                    lines = section.strip().split("\n")
                    if len(lines) < 3:
                        continue

                    exercise = {}

                    # Extract name from first line
                    exercise["name"] = lines[0].split(":", 1)[-1].strip() if ":" in lines[0] else lines[0].strip()

                    # Process remaining lines
                    instructions = []
                    for line in lines[1:]:
                        if "type:" in line.lower() or "exercise type:" in line.lower():
                            exercise["type"] = line.split(":", 1)[-1].strip()
                        elif "equipment:" in line.lower() or "required equipment:" in line.lower():
                            exercise["equipment"] = line.split(":", 1)[-1].strip()
                        elif "description:" in line.lower():
                            exercise["description"] = line.split(":", 1)[-1].strip()
                        elif "instruction" in line.lower() and ":" in line:
                            continue  # Skip the header
                        elif "set" in line.lower() and "rep" in line.lower():
                            parts = line.split(":", 1)
                            if len(parts) > 1:
                                rec = parts[1].strip()
                                if "set" in rec.lower():
                                    try:
                                        sets = int(rec.split("sets")[0].strip().split()[-1])
                                        exercise["sets"] = sets
                                    except:
                                        pass

                                    if "rep" in rec.lower():
                                        try:
                                            reps = rec.split("reps")[0].split("sets")[-1].strip()
                                            exercise["reps"] = reps
                                        except:
                                            pass
                        elif line.strip() and not any(k in line.lower() for k in ["exercise", "name:"]):
                            # Assume it's an instruction step
                            if line.strip().startswith(("- ", "• ", "* ")):
                                instructions.append(line.strip()[2:])
                            elif line.strip()[0].isdigit() and len(line) > 2 and line[1] in [".", ")"]:
                                instructions.append(line.strip()[2:].strip())
                            else:
                                instructions.append(line.strip())

                    if instructions:
                        exercise["instructions"] = instructions

                    if exercise.get("name"):  # Only add if we have at least a name
                        exercises.append(ExerciseSuggestion(**exercise))

                elif "advice" in section.lower() or "tip" in section.lower() or "general" in section.lower():
                    # This looks like general advice
                    general_advice = section.strip()

            # Return default exercise if parsing fails
            if not exercises:
                exercises = [
                    ExerciseSuggestion(
                        name="Workout generation failed",
                        description="Please try again with different parameters."
                    )
                ]

            return WorkoutGenerationResponse(
                exercises=exercises,
                generalAdvice=general_advice
            )

        else:
            logger.warning("❌ No text response from Gemini API for workout generation")
            raise HTTPException(status_code=500, detail="Failed to generate workout")

    except Exception as e:
        logger.error(f"🔥 ERROR generating workout: {str(e)}")
        raise HTTPException(status_code=500, detail="Error generating workout")

class CreateWorkoutFromGeneratedRequest(BaseModel):
    exercises: List[ExerciseSuggestion]
    description: Optional[str] = None

@app.post("/workouts/create-from-generated/", response_model=Workout)
async def create_workout_from_generated(
    request: CreateWorkoutFromGeneratedRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new workout from generated exercises"""
    try:
        logger.info(f"Creating workout from {len(request.exercises)} generated exercises")

        # Create a new workout
        now = get_eastern_now()
        db_workout = models.Workout(
            date=now,
            description=request.description or f"Generated workout: {now.strftime('%Y-%m-%d %H:%M')}",
            status=models.WorkoutStatus.in_progress,
            user_id=current_user.id
        )
        db.add(db_workout)
        db.flush()

        # Add all exercises to the workout
        for exercise_data in request.exercises:
            # Map the exercise type from the suggestion to the enum
            exercise_type = exercise_data.type.lower() if exercise_data.type else "strength"
            if exercise_type not in [e.value for e in ExerciseTypeEnum]:
                exercise_type = "strength"  # Default to strength if type is not recognized

            # Always use the matcher for LLM-generated exercises
            matching_exercise = find_matching_exercise(exercise_data.name, db)

            # Use the matched exercise name if found, otherwise use the original
            exercise_name = matching_exercise.name if matching_exercise else exercise_data.name

            # Log the matching result
            if matching_exercise:
                logger.info(f"✅ Matched AI-generated exercise '{exercise_data.name}' to existing exercise '{exercise_name}'")
            else:
                logger.info(f"ℹ️ No match found for '{exercise_data.name}', using as-is")

            db_exercise = models.Exercise(
                name=exercise_name,
                exercise_type=exercise_type,
                workout_id=db_workout.id
            )
            db.add(db_exercise)
            db.flush()  # Flush to get the exercise ID

            # If sets and reps are provided in the exercise suggestion, create a default set
            if exercise_data.sets and exercise_data.reps:
                try:
                    # Try to parse reps as a number if possible
                    reps = int(exercise_data.reps) if exercise_data.reps.isdigit() else None

                    db_set = models.ExerciseSet(
                        reps=reps,
                        exercise_id=db_exercise.id,
                        notes=f"Suggested: {exercise_data.sets} sets of {exercise_data.reps} reps"
                    )
                    db.add(db_set)
                except (ValueError, AttributeError):
                    # If parsing fails, just continue without adding a set
                    pass

        db.commit()
        db.refresh(db_workout)

        logger.info(f"✅ Successfully created workout {db_workout.id} with {len(request.exercises)} exercises")
        return db_workout

    except Exception as e:
        logger.error(f"🔥 ERROR creating workout from generated exercises: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating workout")

# Workout Planning Endpoints
@app.post("/workout-plans/generate/", response_model=WorkoutPlanGenerationResponse)
async def generate_workout_plan(
    request: WorkoutPlanGenerationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate a comprehensive workout plan using Gemini LLM"""
    try:
        logger.info(f"Generating workout plan for user {current_user.username}")
        logger.info(f"Goals: {request.goals}")
        logger.info(f"Duration: {request.duration_weeks} weeks")
        logger.info(f"Frequency: {request.frequency_per_week} workouts per week")

        # Import the exercise list utility
        from utils.exercise_list import get_exercises_by_equipment, format_exercises_for_llm

        # Get exercises that can be performed with the specified equipment
        available_exercises = get_exercises_by_equipment(db, request.equipment)
        exercise_list = format_exercises_for_llm(available_exercises)

        # Calculate total workouts needed
        total_workouts = request.duration_weeks * request.frequency_per_week

        # Build prompt for Gemini
        prompt = f"""
As an expert personal trainer, create a comprehensive {request.duration_weeks}-week workout plan for a user with the following requirements:

USER GOALS: {request.goals}
DURATION: {request.duration_weeks} weeks
FREQUENCY: {request.frequency_per_week} workouts per week
TOTAL WORKOUTS: {total_workouts}
EQUIPMENT AVAILABLE: {', '.join(request.equipment) if request.equipment else 'Bodyweight only'}

AVAILABLE EXERCISES:
{exercise_list}

Create a structured workout plan that:
1. Addresses the user's specific goals
2. Provides progressive overload over the {request.duration_weeks} weeks
3. Balances different muscle groups appropriately
4. Uses only the available exercises listed above

For each workout, provide:
- Day number (1, 2, 3, etc.)
- Focus/theme (e.g., "Upper Body Strength", "Cardio & Core", etc.)
- 4-6 exercises from the available list
- Sets and reps recommendations
- Brief notes about progression

Format your response as follows:

PLAN_NAME: [Descriptive name for the plan]
PLAN_DESCRIPTION: [Brief overview of the plan approach]

WORKOUT_1:
FOCUS: [Workout focus/theme]
DATE_OFFSET: 0
EXERCISES:
- [Exercise Name]: [Sets] x [Reps] - [Notes]
- [Exercise Name]: [Sets] x [Reps] - [Notes]
[Continue for all exercises]

WORKOUT_2:
FOCUS: [Workout focus/theme]
DATE_OFFSET: [Days from start]
EXERCISES:
- [Exercise Name]: [Sets] x [Reps] - [Notes]
[Continue for all exercises]

[Continue for all {total_workouts} workouts]

GENERAL_ADVICE: [Overall tips for following this plan]
"""

        # Generate content using the Gemini API
        logger.info("🌐 Sending workout plan request to Gemini API")

        response = gemini_service.model.generate_content(
            prompt,
            generation_config={
                "temperature": 0.3,
                "top_p": 0.95,
                "max_output_tokens": 4096,
            }
        )

        if response and hasattr(response, 'text'):
            generated_text = response.text
            logger.info("✅ Workout plan generated successfully")

            # Parse the response to extract plan details
            plan_name, plan_description, planned_workouts_data = _parse_workout_plan_response(generated_text, request)

            # Create the workout plan in the database
            start_date = get_eastern_now().date()
            end_date = start_date + timedelta(weeks=request.duration_weeks)

            db_workout_plan = models.WorkoutPlan(
                user_id=current_user.id,
                name=plan_name,
                description=plan_description,
                start_date=start_date,
                end_date=end_date,
                status=models.WorkoutPlanStatus.active,
                raw_text=generated_text
            )
            db.add(db_workout_plan)
            db.flush()

            # Create planned workouts
            planned_workouts = []
            for workout_data in planned_workouts_data:
                planned_date = start_date + timedelta(days=workout_data['date_offset'])

                db_planned_workout = models.PlannedWorkout(
                    workout_plan_id=db_workout_plan.id,
                    planned_date=planned_date,
                    day_of_plan=workout_data['day_number'],
                    exercises=workout_data['exercises'],
                    status=models.PlannedWorkoutStatus.scheduled
                )
                db.add(db_planned_workout)
                planned_workouts.append(db_planned_workout)

            db.commit()
            db.refresh(db_workout_plan)

            # Refresh planned workouts
            for pw in planned_workouts:
                db.refresh(pw)

            return WorkoutPlanGenerationResponse(
                workout_plan=db_workout_plan,
                planned_workouts=planned_workouts,
                raw_llm_response=generated_text
            )

        else:
            logger.warning("❌ No text response from Gemini API for workout plan generation")
            raise HTTPException(status_code=500, detail="Failed to generate workout plan")

    except Exception as e:
        logger.error(f"🔥 ERROR generating workout plan: {str(e)}")
        raise HTTPException(status_code=500, detail="Error generating workout plan")

def _parse_workout_plan_response(generated_text: str, request: WorkoutPlanGenerationRequest):
    """Parse the LLM response to extract workout plan details"""
    try:
        lines = generated_text.split('\n')

        # Extract plan name and description
        plan_name = "Custom Workout Plan"
        plan_description = f"{request.duration_weeks}-week workout plan"

        for line in lines:
            if line.startswith("PLAN_NAME:"):
                plan_name = line.replace("PLAN_NAME:", "").strip()
            elif line.startswith("PLAN_DESCRIPTION:"):
                plan_description = line.replace("PLAN_DESCRIPTION:", "").strip()

        # Extract workouts
        planned_workouts = []
        current_workout = None
        current_exercises = []

        for line in lines:
            line = line.strip()

            if line.startswith("WORKOUT_"):
                # Save previous workout if exists
                if current_workout:
                    current_workout['exercises'] = current_exercises
                    planned_workouts.append(current_workout)

                # Start new workout
                workout_num = int(line.split("_")[1].replace(":", ""))
                current_workout = {
                    'day_number': workout_num,
                    'date_offset': (workout_num - 1) * (7 // request.frequency_per_week),
                    'focus': '',
                    'exercises': []
                }
                current_exercises = []

            elif line.startswith("FOCUS:") and current_workout:
                current_workout['focus'] = line.replace("FOCUS:", "").strip()

            elif line.startswith("DATE_OFFSET:") and current_workout:
                try:
                    current_workout['date_offset'] = int(line.replace("DATE_OFFSET:", "").strip())
                except ValueError:
                    pass  # Keep calculated offset

            elif line.startswith("- ") and current_workout:
                # Parse exercise line
                exercise_line = line[2:].strip()
                if ":" in exercise_line:
                    name_part, details_part = exercise_line.split(":", 1)
                    exercise_name = name_part.strip()

                    # Parse sets/reps and notes
                    sets_reps = ""
                    notes = ""
                    if " - " in details_part:
                        sets_reps, notes = details_part.split(" - ", 1)
                    else:
                        sets_reps = details_part

                    current_exercises.append({
                        'name': exercise_name,
                        'exercise_type': 'strength',  # Default type
                        'sets': None,
                        'reps': sets_reps.strip(),
                        'notes': notes.strip() if notes else None
                    })

        # Don't forget the last workout
        if current_workout:
            current_workout['exercises'] = current_exercises
            planned_workouts.append(current_workout)

        return plan_name, plan_description, planned_workouts

    except Exception as e:
        logger.error(f"Error parsing workout plan response: {e}")
        # Return fallback data
        return "Custom Workout Plan", f"{request.duration_weeks}-week workout plan", []

@app.get("/workout-plans/", response_model=List[WorkoutPlan])
def get_user_workout_plans(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all workout plans for the current user"""
    workout_plans = db.query(models.WorkoutPlan).filter(
        models.WorkoutPlan.user_id == current_user.id
    ).order_by(models.WorkoutPlan.start_date.desc()).all()

    return workout_plans

@app.get("/workout-plans/{plan_id}", response_model=WorkoutPlanDetail)
def get_workout_plan_detail(
    plan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific workout plan"""
    workout_plan = db.query(models.WorkoutPlan).filter(
        models.WorkoutPlan.id == plan_id,
        models.WorkoutPlan.user_id == current_user.id
    ).first()

    if not workout_plan:
        raise HTTPException(status_code=404, detail="Workout plan not found")

    # Get all planned workouts for this plan
    planned_workouts = db.query(models.PlannedWorkout).filter(
        models.PlannedWorkout.workout_plan_id == plan_id
    ).order_by(models.PlannedWorkout.planned_date).all()

    # Create response with planned workouts
    response = WorkoutPlanDetail(
        id=workout_plan.id,
        user_id=workout_plan.user_id,
        name=workout_plan.name,
        description=workout_plan.description,
        start_date=workout_plan.start_date,
        end_date=workout_plan.end_date,
        status=workout_plan.status,
        raw_text=workout_plan.raw_text,
        planned_workouts=planned_workouts
    )

    return response

@app.put("/workout-plans/{plan_id}/status")
def update_workout_plan_status(
    plan_id: int,
    status: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update the status of a workout plan"""
    workout_plan = db.query(models.WorkoutPlan).filter(
        models.WorkoutPlan.id == plan_id,
        models.WorkoutPlan.user_id == current_user.id
    ).first()

    if not workout_plan:
        raise HTTPException(status_code=404, detail="Workout plan not found")

    # Validate status
    valid_statuses = ["active", "completed", "paused", "cancelled"]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {valid_statuses}")

    workout_plan.status = status
    db.commit()

    return {"message": f"Workout plan status updated to {status}"}

@app.delete("/workout-plans/{plan_id}")
def delete_workout_plan(
    plan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a workout plan and all its planned workouts"""
    workout_plan = db.query(models.WorkoutPlan).filter(
        models.WorkoutPlan.id == plan_id,
        models.WorkoutPlan.user_id == current_user.id
    ).first()

    if not workout_plan:
        raise HTTPException(status_code=404, detail="Workout plan not found")

    # Delete the workout plan (planned workouts will be deleted due to cascade)
    db.delete(workout_plan)
    db.commit()

    return {"message": "Workout plan deleted successfully"}

@app.get("/planned-workouts/", response_model=List[PlannedWorkout])
def get_planned_workouts(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get planned workouts for a date range"""
    query = db.query(models.PlannedWorkout).join(models.WorkoutPlan).filter(
        models.WorkoutPlan.user_id == current_user.id
    )

    # Apply date filters if provided
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(models.PlannedWorkout.planned_date >= start_dt)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid start_date format")

    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(models.PlannedWorkout.planned_date <= end_dt)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid end_date format")

    planned_workouts = query.order_by(models.PlannedWorkout.planned_date).all()
    return planned_workouts

@app.put("/planned-workouts/{planned_workout_id}/complete")
def mark_planned_workout_complete(
    planned_workout_id: int,
    workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark a planned workout as completed and link it to an actual workout"""
    # Get the planned workout
    planned_workout = db.query(models.PlannedWorkout).join(models.WorkoutPlan).filter(
        models.PlannedWorkout.id == planned_workout_id,
        models.WorkoutPlan.user_id == current_user.id
    ).first()

    if not planned_workout:
        raise HTTPException(status_code=404, detail="Planned workout not found")

    # Verify the workout belongs to the user
    workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()

    if not workout:
        raise HTTPException(status_code=404, detail="Workout not found")

    # Update the planned workout
    planned_workout.status = models.PlannedWorkoutStatus.completed
    planned_workout.workout_id = workout_id
    db.commit()

    return {"message": "Planned workout marked as completed"}

@app.post("/planned-workouts/{planned_workout_id}/start", response_model=Workout)
def start_workout_from_plan(
    planned_workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Start a new workout based on a planned workout"""
    # Get the planned workout
    planned_workout = db.query(models.PlannedWorkout).join(models.WorkoutPlan).filter(
        models.PlannedWorkout.id == planned_workout_id,
        models.WorkoutPlan.user_id == current_user.id
    ).first()

    if not planned_workout:
        raise HTTPException(status_code=404, detail="Planned workout not found")

    # Check if there's already an active workout
    existing_workout = db.query(models.Workout).filter(
        models.Workout.user_id == current_user.id,
        models.Workout.status == models.WorkoutStatus.in_progress
    ).first()

    if existing_workout:
        raise HTTPException(status_code=400, detail="You already have an active workout. Complete it first.")

    # Create a new workout based on the planned workout
    now = get_eastern_now()
    db_workout = models.Workout(
        date=now,
        description=f"Planned workout: Day {planned_workout.day_of_plan}",
        status=models.WorkoutStatus.in_progress,
        user_id=current_user.id,
        planned_workout_id=planned_workout.id  # Link to the planned workout
    )
    db.add(db_workout)
    db.flush()

    # Add exercises from the planned workout
    planned_exercises = planned_workout.exercises
    if isinstance(planned_exercises, str):
        import json
        planned_exercises = json.loads(planned_exercises)

    for planned_exercise in planned_exercises:
        db_exercise = models.Exercise(
            name=planned_exercise.get('name', ''),
            exercise_type=planned_exercise.get('exercise_type', 'strength'),
            workout_id=db_workout.id
        )
        db.add(db_exercise)

    db.commit()
    db.refresh(db_workout)

    return db_workout

@app.put("/planned-workouts/{planned_workout_id}/skip")
def skip_planned_workout(
    planned_workout_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark a planned workout as skipped"""
    # Get the planned workout
    planned_workout = db.query(models.PlannedWorkout).join(models.WorkoutPlan).filter(
        models.PlannedWorkout.id == planned_workout_id,
        models.WorkoutPlan.user_id == current_user.id
    ).first()

    if not planned_workout:
        raise HTTPException(status_code=404, detail="Planned workout not found")

    # Update the planned workout status
    planned_workout.status = models.PlannedWorkoutStatus.skipped
    db.commit()

    return {"message": "Planned workout marked as skipped"}

# Exercise Endpoints
@app.post("/workouts/{workout_id}/exercises/", response_model=Exercise)
def add_exercise(
    workout_id: int,
    exercise: ExerciseCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Add an exercise to a workout."""
    db_workout = db.query(models.Workout).filter(
        models.Workout.id == workout_id,
        models.Workout.user_id == current_user.id
    ).first()

    if db_workout is None:
        raise HTTPException(status_code=404, detail="Workout not found")

    # Only use the exercise matcher for LLM-generated exercises
    if exercise.from_llm:
        # Try to find a matching exercise in the database
        matching_exercise = find_matching_exercise(exercise.name, db)

        # Use the matched exercise name if found, otherwise use the original
        exercise_name = matching_exercise.name if matching_exercise else exercise.name

        # Log the matching result
        if matching_exercise:
            logger.info(f"✅ Matched LLM-generated exercise '{exercise.name}' to existing exercise '{exercise_name}'")
        else:
            logger.info(f"ℹ️ No match found for LLM-generated exercise '{exercise.name}', using as-is")
    else:
        # For user-selected exercises, use the name exactly as provided
        exercise_name = exercise.name
        logger.info(f"➡️ Using user-selected exercise name as-is: '{exercise_name}'")

    db_exercise = models.Exercise(
        name=exercise_name,
        exercise_type=exercise.exercise_type,
        workout_id=workout_id
    )
    db.add(db_exercise)
    db.flush()

    # Add exercise sets
    for set_data in exercise.sets:
        db_set = models.ExerciseSet(
            reps=set_data.reps,
            weight=set_data.weight,
            duration_seconds=set_data.duration_seconds,
            distance=set_data.distance,
            notes=set_data.notes,
            exercise_id=db_exercise.id
        )
        db.add(db_set)

    db.commit()
    db.refresh(db_exercise)
    return db_exercise

@app.delete("/exercises/{exercise_id}")
def delete_exercise(
    exercise_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete an exercise from a workout."""
    # First verify the exercise belongs to the user
    exercise = db.query(models.Exercise).join(models.Workout).filter(
        models.Exercise.id == exercise_id,
        models.Workout.user_id == current_user.id
    ).first()

    if exercise is None:
        raise HTTPException(status_code=404, detail="Exercise not found or doesn't belong to you")

    db.delete(exercise)
    db.commit()
    return {"detail": "Exercise deleted successfully"}

# Set Endpoints
@app.post("/exercises/{exercise_id}/sets/", response_model=ExerciseSet)
def add_set(
    exercise_id: int,
    exercise_set: ExerciseSetCreate,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    logger.info(f"➡️ Entered POST /exercises/{exercise_id}/sets/")

    # Log all headers
    print("📥 Headers:")
    for key, value in request.headers.items():
        print(f"  {key}: {value}")
    logger.info(f"Request headers: {dict(request.headers)}")

    # Log incoming body data
    logger.info(f"📦 Received exercise_set payload: {exercise_set.dict()}")

    try:
        # Confirm exercise belongs to user
        logger.info(f"🔍 Verifying exercise {exercise_id} for user {current_user.id}")
        exercise = db.query(models.Exercise).join(models.Workout).filter(
            models.Exercise.id == exercise_id,
            models.Workout.user_id == current_user.id
        ).first()

        if exercise is None:
            logger.warning(f"❌ Exercise {exercise_id} not found or doesn't belong to user {current_user.id}")
            raise HTTPException(status_code=404, detail="Exercise not found or doesn't belong to you")

        logger.info(f"✅ Exercise {exercise_id} verified, creating set...")

        # Create new set
        db_set = models.ExerciseSet(
            reps=exercise_set.reps,
            weight=exercise_set.weight,
            duration_seconds=exercise_set.duration_seconds,
            distance=exercise_set.distance,
            notes=exercise_set.notes,
            exercise_id=exercise_id
        )
        db.add(db_set)
        db.commit()
        db.refresh(db_set)

        logger.info(f"✅ Successfully added set {db_set.id} to exercise {exercise_id}")
        return db_set

    except Exception as e:
        logger.error(f"🔥 Exception in add_set(): {e}")
        return JSONResponse(status_code=500, content={"detail": "Internal Server Error", "error": str(e)})

@app.delete("/sets/{set_id}")
def delete_set(
    set_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a set from an exercise."""
    # First verify the set belongs to the user
    set = db.query(models.ExerciseSet).join(models.Exercise).join(models.Workout).filter(
        models.ExerciseSet.id == set_id,
        models.Workout.user_id == current_user.id
    ).first()

    if set is None:
        raise HTTPException(status_code=404, detail="Set not found or doesn't belong to you")

    db.delete(set)
    db.commit()
    return {"detail": "Set deleted successfully"}

# Social Feed Endpoint
@app.get("/workouts/social-feed/", response_model=List[SocialWorkout])
def get_social_feed(
    days: int = 7,
    limit: int = 20,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to retrieve recent workouts from all users for the social feed.

    Args:
        days: Number of days to look back (default: 7)
        limit: Maximum number of workouts to return (default: 20)
    """
    # Calculate the date threshold (7 days ago by default)
    date_threshold = get_eastern_now() - timedelta(days=days)

    # Query for completed workouts from all users within the time period
    social_workouts_query = (
        db.query(models.Workout, models.User.username)
        .join(models.User)
        .filter(
            models.Workout.status == models.WorkoutStatus.completed,
            models.Workout.date >= date_threshold
        )
        .order_by(models.Workout.date.desc())
        .limit(limit)
    )

    # Execute the query
    social_workouts_results = social_workouts_query.all()

    # Convert the results to SocialWorkout objects
    social_workouts = []
    for workout, username in social_workouts_results:
        # Create a dictionary with all workout attributes
        workout_dict = {
            "id": workout.id,
            "user_id": workout.user_id,
            "date": workout.date,
            "description": workout.description,
            "status": workout.status,
            "exercises": workout.exercises,
            "username": username
        }
        social_workouts.append(SocialWorkout(**workout_dict))

    return social_workouts

# Statistics Endpoints
@app.get("/workouts/statistics/", response_model=WorkoutStatistics)
def get_workout_statistics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to retrieve workout statistics."""
    # Get all user's workouts
    workouts = db.query(models.Workout).filter(
        models.Workout.user_id == current_user.id
    ).all()

    # Calculate total workouts
    total_workouts = len(workouts)

    # Calculate unique days active
    unique_workout_days = len(set(workout.date.date() for workout in workouts))

    # Most frequent workout type (based on exercises)
    most_frequent_type = None
    if total_workouts > 0:
        exercise_types = []
        for workout in workouts:
            for exercise in workout.exercises:
                exercise_types.append(exercise.exercise_type)

        if exercise_types:
            # Get the most common exercise type
            from collections import Counter
            most_frequent_type = Counter(exercise_types).most_common(1)[0][0]

    # Average workout description length (as a proxy for workout complexity)
    avg_workout_length = sum(len(workout.description or "") for workout in workouts) / total_workouts if total_workouts > 0 else 0

    return WorkoutStatistics(
        total_workouts=total_workouts,
        total_days_active=unique_workout_days,
        most_frequent_workout_type=most_frequent_type,
        average_workout_length=avg_workout_length
    )

@app.get("/workouts/personal-records/", response_model=List[PersonalRecord])
def get_personal_records(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Endpoint to retrieve user's personal records."""
    # Get all completed workouts for the user
    workouts = db.query(models.Workout).filter(
        models.Workout.user_id == current_user.id,
        models.Workout.status == models.WorkoutStatus.completed
    ).all()

    # Dictionary to track personal records by exercise
    personal_records = {}

    # Process all workouts to find personal records
    for workout in workouts:
        for exercise in workout.exercises:
            # For strength exercises, track max weight
            if exercise.exercise_type == models.ExerciseType.strength:
                for set_data in exercise.sets:
                    if set_data.weight is not None and set_data.reps is not None:
                        # Normalize the exercise name for consistent comparison
                        normalized_name = normalize_exercise_name(exercise.name)

                        # Create a unique key for this exercise using the normalized name
                        exercise_key = f"{normalized_name}_strength"

                        # Check if this is a new record
                        if (exercise_key not in personal_records or
                            set_data.weight > personal_records[exercise_key]["value"]):
                            personal_records[exercise_key] = {
                                "exercise_name": exercise.name,
                                "exercise_type": "strength",
                                "value": set_data.weight,
                                "unit": "lbs",
                                "date": workout.date,
                                "workout_id": workout.id
                            }

            # For cardio exercises, track max distance and min duration
            elif exercise.exercise_type == models.ExerciseType.cardio:
                for set_data in exercise.sets:
                    # Track max distance
                    if set_data.distance is not None:
                        # Normalize the exercise name for consistent comparison
                        normalized_name = normalize_exercise_name(exercise.name)

                        # Create a unique key using the normalized name
                        distance_key = f"{normalized_name}_distance"
                        if (distance_key not in personal_records or
                            set_data.distance > personal_records[distance_key]["value"]):
                            personal_records[distance_key] = {
                                "exercise_name": exercise.name,
                                "exercise_type": "cardio_distance",
                                "value": set_data.distance,
                                "unit": "miles",
                                "date": workout.date,
                                "workout_id": workout.id
                            }

                    # Track best pace (if both distance and duration are available)
                    if set_data.distance is not None and set_data.duration_seconds is not None and set_data.distance > 0:
                        # Normalize the exercise name for consistent comparison
                        normalized_name = normalize_exercise_name(exercise.name)

                        # Create a unique key using the normalized name
                        pace_key = f"{normalized_name}_pace"
                        pace = set_data.duration_seconds / set_data.distance  # seconds per mile

                        if (pace_key not in personal_records or
                            pace < personal_records[pace_key]["value"]):
                            personal_records[pace_key] = {
                                "exercise_name": exercise.name,
                                "exercise_type": "cardio_pace",
                                "value": pace,
                                "unit": "sec/mile",
                                "date": workout.date,
                                "workout_id": workout.id
                            }

    # Convert dictionary to list of PersonalRecord objects
    records_list = [PersonalRecord(**record) for record in personal_records.values()]

    return records_list

# Exercise Library Models
class MuscleGroupBase(BaseModel):
    name: str
    description: Optional[str] = None

class MuscleGroupCreate(MuscleGroupBase):
    pass

class MuscleGroup(MuscleGroupBase):
    id: int

    class Config:
        from_attributes = True

class EquipmentBase(BaseModel):
    name: str
    description: Optional[str] = None

class EquipmentCreate(EquipmentBase):
    pass

class Equipment(EquipmentBase):
    id: int

    class Config:
        from_attributes = True

class ExerciseLibraryBase(BaseModel):
    name: str
    description: Optional[str] = None
    instructions: Optional[str] = None
    exercise_type: ExerciseTypeEnum
    difficulty: Optional[str] = None

class ExerciseLibraryCreate(ExerciseLibraryBase):
    muscle_group_ids: List[int] = []
    equipment_ids: List[int] = []

class ExerciseLibraryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    instructions: Optional[str] = None
    exercise_type: Optional[ExerciseTypeEnum] = None
    difficulty: Optional[str] = None
    muscle_group_ids: Optional[List[int]] = None
    equipment_ids: Optional[List[int]] = None

class ExerciseLibraryResponse(ExerciseLibraryBase):
    id: int
    muscle_groups: List[MuscleGroup] = []
    equipment: List[Equipment] = []

    class Config:
        from_attributes = True

# Exercise Library Endpoints
@app.get("/exercise-library/", response_model=List[ExerciseLibraryResponse])
def get_exercise_library(
    skip: int = 0,
    limit: int = 100,
    exercise_type: Optional[str] = None,
    muscle_group: Optional[str] = None,
    equipment: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all exercises from the library with optional filtering"""
    query = db.query(models.ExerciseLibrary)

    # Apply filters if provided
    if exercise_type:
        query = query.filter(models.ExerciseLibrary.exercise_type == exercise_type)

    if muscle_group:
        query = query.join(models.exercise_muscle_groups).join(models.MuscleGroup).filter(
            models.MuscleGroup.name == muscle_group
        )

    if equipment:
        query = query.join(models.exercise_equipment).join(models.Equipment).filter(
            models.Equipment.name == equipment
        )

    if search:
        search_term = f"%{search}%"
        query = query.filter(models.ExerciseLibrary.name.ilike(search_term))

    exercises = query.offset(skip).limit(limit).all()
    return exercises

@app.get("/exercise-library/{exercise_id}", response_model=ExerciseLibraryResponse)
def get_exercise(
    exercise_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific exercise from the library"""
    exercise = db.query(models.ExerciseLibrary).filter(models.ExerciseLibrary.id == exercise_id).first()
    if exercise is None:
        raise HTTPException(status_code=404, detail="Exercise not found")
    return exercise

@app.get("/muscle-groups/", response_model=List[MuscleGroup])
def get_muscle_groups(
    db: Session = Depends(get_db)
):
    """Get all muscle groups"""
    return db.query(models.MuscleGroup).all()

@app.get("/equipment/", response_model=List[Equipment])
def get_equipment(
    db: Session = Depends(get_db)
):
    """Get all equipment"""
    return db.query(models.Equipment).all()

# Define a model for exercise-muscle group mapping
class ExerciseMuscleGroupMapping(BaseModel):
    exercise_name: str
    muscle_groups: List[str]

# Import exercise mapper functions
# import exercise_mapper  # Removed during cleanup

@app.get("/exercise-muscle-mappings/", response_model=List[ExerciseMuscleGroupMapping])
def get_exercise_muscle_mappings(
    db: Session = Depends(get_db)
):
    """
    Get mappings between exercises and their associated muscle groups.
    This endpoint provides data for frontend visualizations.
    """
    # Query all exercises with their muscle groups
    exercises = db.query(models.ExerciseLibrary).all()

    # Format the response
    mappings = []
    for exercise in exercises:
        muscle_group_names = [mg.name for mg in exercise.muscle_groups]
        mappings.append({
            "exercise_name": exercise.name,
            "muscle_groups": muscle_group_names
        })

    return mappings

# Define a model for exercise max weight response
class ExerciseMaxWeight(BaseModel):
    exercise_name: str
    all_time_max: Optional[float] = None
    all_time_max_date: Optional[datetime] = None
    previous_max: Optional[float] = None
    previous_max_date: Optional[datetime] = None
    has_history: bool = False

@app.get("/exercises/max-weight/{exercise_name}", response_model=ExerciseMaxWeight)
def get_exercise_max_weight(
    exercise_name: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get the max weight ever used for a specific exercise by the current user."""
    # Get all completed workouts for the user
    workouts = db.query(models.Workout).filter(
        models.Workout.user_id == current_user.id,
        models.Workout.status == models.WorkoutStatus.completed
    ).order_by(models.Workout.date.desc()).all()

    all_time_max = None
    all_time_max_date = None
    previous_max = None
    previous_max_date = None
    found_exercise = False

    # Process all workouts to find max weights for this exercise
    for workout in workouts:
        for exercise in workout.exercises:
            # Case-insensitive comparison to handle naming variations
            if exercise.name.lower() == exercise_name.lower() and exercise.exercise_type == models.ExerciseType.strength:
                found_exercise = True

                # Find max weight in this workout
                workout_max = None
                for set_data in exercise.sets:
                    if set_data.weight is not None:
                        if workout_max is None or set_data.weight > workout_max:
                            workout_max = set_data.weight

                if workout_max is not None:
                    # Update all-time max if needed
                    if all_time_max is None or workout_max > all_time_max:
                        all_time_max = workout_max
                        all_time_max_date = workout.date

                    # Set previous max (from the most recent workout with this exercise)
                    # This will be set only once since workouts are ordered by date desc
                    if previous_max is None:
                        previous_max = workout_max
                        previous_max_date = workout.date

    return {
        "exercise_name": exercise_name,
        "all_time_max": all_time_max,
        "all_time_max_date": all_time_max_date,
        "previous_max": previous_max,
        "previous_max_date": previous_max_date,
        "has_history": found_exercise
    }

# Form Analysis Endpoints
@app.post("/form-analysis/", response_model=FormAnalysisResponse)
async def create_form_analysis(
    form_analysis_request: FormAnalysisRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new form analysis session.

    This endpoint processes a video file for form analysis and returns the results.
    """
    try:
        # Verify the exercise exists
        exercise = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.id == form_analysis_request.exercise_id
        ).first()

        if not exercise:
            raise HTTPException(status_code=404, detail="Exercise not found")

        # For now, we'll use a mock implementation since we don't have actual video upload yet
        # In a real implementation, we would process the uploaded video file

        # Create a new form analysis record
        form_analysis = models.FormAnalysis(
            user_id=current_user.id,
            exercise_id=form_analysis_request.exercise_id,
            status=models.FormAnalysisStatus.in_progress
        )
        db.add(form_analysis)
        db.commit()
        db.refresh(form_analysis)

        # Return the form analysis ID for the client to use in subsequent requests
        return {
            "id": form_analysis.id,
            "overall_score": 0.0,
            "feedback": "Form analysis in progress. Please upload a video.",
            "recommendations": "",
            "issues": [],
            "analysis_data": None
        }
    except Exception as e:
        logger.error(f"Error creating form analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating form analysis: {str(e)}")

@app.post("/form-analysis/{form_analysis_id}/upload", response_model=FormAnalysisResponse)
async def upload_form_video(
    form_analysis_id: int,
    video: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Upload a video for form analysis.

    This endpoint receives a video file, processes it for form analysis, and returns the results.
    """
    try:
        logger.info(f"Received video upload request for form analysis ID: {form_analysis_id}")
        logger.info(f"Video file name: {video.filename}, content type: {video.content_type}")

        # Verify the form analysis exists and belongs to the user
        form_analysis = db.query(models.FormAnalysis).filter(
            models.FormAnalysis.id == form_analysis_id,
            models.FormAnalysis.user_id == current_user.id
        ).first()

        if not form_analysis:
            logger.error(f"Form analysis not found: {form_analysis_id}")
            raise HTTPException(status_code=404, detail="Form analysis not found")

        # Save the video to a temporary file
        temp_video_path = f"/tmp/form_analysis_{form_analysis_id}.mp4"
        logger.info(f"Saving video to temporary file: {temp_video_path}")

        # Read the file content
        video_content = await video.read()
        logger.info(f"Read {len(video_content)} bytes from uploaded file")

        # Write to temporary file
        with open(temp_video_path, "wb") as f:
            f.write(video_content)

        logger.info(f"Video saved to temporary file, processing...")

        # Process the video
        results = form_analysis_service.process_video(
            temp_video_path,
            current_user.id,
            form_analysis.exercise_id,
            db
        )

        # Delete the temporary file
        import os
        if os.path.exists(temp_video_path):
            os.remove(temp_video_path)
            logger.info(f"Temporary video file deleted")

        # Return the results
        logger.info(f"Form analysis completed successfully")
        return results
    except Exception as e:
        logger.error(f"Error processing form analysis video: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error processing form analysis video: {str(e)}")

@app.get("/form-analysis/{form_analysis_id}", response_model=FormAnalysisResponse)
async def get_form_analysis(
    form_analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get the results of a form analysis.

    This endpoint retrieves the results of a previously created form analysis.
    """
    try:
        # Verify the form analysis exists and belongs to the user
        form_analysis = db.query(models.FormAnalysis).filter(
            models.FormAnalysis.id == form_analysis_id,
            models.FormAnalysis.user_id == current_user.id
        ).first()

        if not form_analysis:
            raise HTTPException(status_code=404, detail="Form analysis not found")

        # Get the form issues
        issues = db.query(models.FormIssue).filter(
            models.FormIssue.form_analysis_id == form_analysis_id
        ).all()

        # Convert issues to response format
        issue_responses = []
        for issue in issues:
            issue_responses.append(FormIssueResponse(
                issue_type=issue.issue_type,
                severity=issue.severity,
                frame_number=issue.frame_number,
                description=issue.description,
                recommendation=issue.recommendation
            ))

        # Return the form analysis
        return {
            "id": form_analysis.id,
            "overall_score": form_analysis.overall_score or 0.0,
            "feedback": form_analysis.feedback or "",
            "recommendations": form_analysis.recommendations or "",
            "issues": issue_responses,
            "analysis_data": form_analysis.analysis_data
        }
    except Exception as e:
        logger.error(f"Error retrieving form analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving form analysis: {str(e)}")

@app.get("/form-analysis/", response_model=List[FormAnalysisResponse])
async def get_user_form_analyses(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all form analyses for the current user.

    This endpoint retrieves all form analyses created by the current user.
    """
    try:
        # Get all form analyses for the user
        form_analyses = db.query(models.FormAnalysis).filter(
            models.FormAnalysis.user_id == current_user.id
        ).all()

        # Convert to response format
        responses = []
        for analysis in form_analyses:
            # Get the form issues
            issues = db.query(models.FormIssue).filter(
                models.FormIssue.form_analysis_id == analysis.id
            ).all()

            # Convert issues to response format
            issue_responses = []
            for issue in issues:
                issue_responses.append(FormIssueResponse(
                    issue_type=issue.issue_type,
                    severity=issue.severity,
                    frame_number=issue.frame_number,
                    description=issue.description,
                    recommendation=issue.recommendation
                ))

            # Add to responses
            responses.append(FormAnalysisResponse(
                id=analysis.id,
                overall_score=analysis.overall_score or 0.0,
                feedback=analysis.feedback or "",
                recommendations=analysis.recommendations or "",
                issues=issue_responses,
                analysis_data=analysis.analysis_data
            ))

        return responses
    except Exception as e:
        logger.error(f"Error retrieving form analyses: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving form analyses: {str(e)}")

@app.post("/form-rules/{exercise_id}", response_model=dict)
async def create_or_update_form_rules(
    exercise_id: int,
    rules: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create or update form analysis rules for a specific exercise.

    This endpoint allows setting custom form analysis rules for an exercise.
    """
    try:
        # Verify the exercise exists
        exercise = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.id == exercise_id
        ).first()

        if not exercise:
            raise HTTPException(status_code=404, detail="Exercise not found")

        # Check if rules already exist for this exercise
        existing_rules = db.query(models.FormRules).filter(
            models.FormRules.exercise_id == exercise_id
        ).first()

        if existing_rules:
            # Update existing rules
            for key, value in rules.items():
                if hasattr(existing_rules, key):
                    setattr(existing_rules, key, value)

            # Update additional_rules JSON field with any extra rules
            additional_rules = {}
            for key, value in rules.items():
                if not hasattr(existing_rules, key):
                    additional_rules[key] = value

            if additional_rules:
                existing_rules.additional_rules = additional_rules

            db.commit()
            return {"message": "Form rules updated successfully", "exercise_id": exercise_id}
        else:
            # Create new rules
            # Extract known fields
            known_fields = {
                "knee_min_angle", "knee_max_angle", "hip_min_angle", "hip_max_angle",
                "back_min_angle", "back_max_angle", "knee_tracking_threshold", "bar_path_threshold"
            }

            rule_data = {k: v for k, v in rules.items() if k in known_fields}

            # Put remaining fields in additional_rules
            additional_rules = {k: v for k, v in rules.items() if k not in known_fields}

            # Create the rules object
            new_rules = models.FormRules(
                exercise_id=exercise_id,
                **rule_data,
                additional_rules=additional_rules if additional_rules else None
            )

            db.add(new_rules)
            db.commit()

            return {"message": "Form rules created successfully", "exercise_id": exercise_id}
    except Exception as e:
        logger.error(f"Error creating/updating form rules: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating/updating form rules: {str(e)}")

@app.get("/form-rules/{exercise_id}", response_model=dict)
async def get_form_rules(
    exercise_id: int,
    db: Session = Depends(get_db)
):
    """
    Get form analysis rules for a specific exercise.

    This endpoint retrieves the form analysis rules for an exercise.
    """
    try:
        # Verify the exercise exists
        exercise = db.query(models.ExerciseLibrary).filter(
            models.ExerciseLibrary.id == exercise_id
        ).first()

        if not exercise:
            raise HTTPException(status_code=404, detail="Exercise not found")

        # Get the rules
        rules = db.query(models.FormRules).filter(
            models.FormRules.exercise_id == exercise_id
        ).first()

        if not rules:
            # Return default rules for barbell back squat
            if "squat" in exercise.name.lower():
                return {
                    "knee_min_angle": 80,
                    "knee_max_angle": 170,
                    "hip_min_angle": 50,
                    "hip_max_angle": 170,
                    "back_min_angle": 45,
                    "back_max_angle": 90,
                    "knee_tracking_threshold": 15,
                    "depth_threshold": 0.95
                }
            else:
                # Return empty rules for other exercises
                return {
                    "message": "No form rules defined for this exercise"
                }

        # Convert to dictionary
        result = {
            "knee_min_angle": rules.knee_min_angle,
            "knee_max_angle": rules.knee_max_angle,
            "hip_min_angle": rules.hip_min_angle,
            "hip_max_angle": rules.hip_max_angle,
            "back_min_angle": rules.back_min_angle,
            "back_max_angle": rules.back_max_angle,
            "knee_tracking_threshold": rules.knee_tracking_threshold,
            "bar_path_threshold": rules.bar_path_threshold
        }

        # Add additional rules
        if rules.additional_rules:
            result.update(rules.additional_rules)

        return result
    except Exception as e:
        logger.error(f"Error retrieving form rules: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving form rules: {str(e)}")

@app.get("/")
def read_root():
    """Root endpoint for API health check."""
    return {
        "message": "Welcome to the Workout Tracker API",
        "status": "healthy",
        "version": "1.0.0"
    }

# Weight Tracking Endpoints
@app.post("/users/me/weight", response_model=UserWeight)
def add_weight_entry(
    weight_data: UserWeightCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Add a new weight entry for the current user."""
    try:
        # Create new weight entry
        db_weight = models.UserWeight(
            user_id=current_user.id,
            weight=weight_data.weight,
            body_fat=weight_data.body_fat,
            date=weight_data.date,
            notes=weight_data.notes
        )

        db.add(db_weight)
        db.commit()
        db.refresh(db_weight)

        return db_weight
    except Exception as e:
        logger.error(f"Error adding weight entry: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error adding weight entry: {str(e)}")

@app.get("/users/me/weight", response_model=List[UserWeight])
def get_weight_entries(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get weight entries for the current user with optional date filtering."""
    try:
        # Start with base query
        query = db.query(models.UserWeight).filter(
            models.UserWeight.user_id == current_user.id
        )

        # Apply date filters if provided
        if start_date:
            query = query.filter(models.UserWeight.date >= start_date)
        if end_date:
            query = query.filter(models.UserWeight.date <= end_date)

        # Order by date (newest first) and apply pagination
        weight_entries = query.order_by(models.UserWeight.date.desc()).offset(skip).limit(limit).all()

        return weight_entries
    except Exception as e:
        logger.error(f"Error retrieving weight entries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving weight entries: {str(e)}")

@app.put("/users/me/weight/{weight_id}", response_model=UserWeight)
def update_weight_entry(
    weight_id: int,
    weight_data: UserWeightCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a specific weight entry for the current user."""
    try:
        # Find the weight entry
        weight_entry = db.query(models.UserWeight).filter(
            models.UserWeight.id == weight_id,
            models.UserWeight.user_id == current_user.id
        ).first()

        if not weight_entry:
            raise HTTPException(status_code=404, detail="Weight entry not found")

        # Update fields
        weight_entry.weight = weight_data.weight
        weight_entry.body_fat = weight_data.body_fat
        weight_entry.date = weight_data.date
        weight_entry.notes = weight_data.notes

        db.commit()
        db.refresh(weight_entry)

        return weight_entry
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating weight entry: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating weight entry: {str(e)}")

@app.delete("/users/me/weight/{weight_id}", response_model=dict)
def delete_weight_entry(
    weight_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a specific weight entry for the current user."""
    try:
        # Find the weight entry
        weight_entry = db.query(models.UserWeight).filter(
            models.UserWeight.id == weight_id,
            models.UserWeight.user_id == current_user.id
        ).first()

        if not weight_entry:
            raise HTTPException(status_code=404, detail="Weight entry not found")

        # Delete the entry
        db.delete(weight_entry)
        db.commit()

        return {"message": "Weight entry deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting weight entry: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting weight entry: {str(e)}")