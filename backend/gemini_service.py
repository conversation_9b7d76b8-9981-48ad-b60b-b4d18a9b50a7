# backend/gemini_service.py
import os
import logging
import google.generativeai as genai
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import statistics
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Changed from DEBUG to INFO to reduce verbosity
    format='%(asctime)s | %(levelname)8s | %(message)s',
    handlers=[
        logging.StreamHandler()  # Removed FileHandler to stop writing to log file
    ]
)
logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Google's Gemini API for workout analysis"""

    def __init__(self):
        # Get API key and configuration
        self.api_key = os.getenv("GEMINI_API_KEY")
        self.mock_mode = os.getenv("MOCK_ANALYSIS", "false").lower() == "true"

        if not self.api_key:
            logger.critical("🚨 CRITICAL: GEMINI_API_KEY environment variable NOT SET!")
            logger.critical("Set MOCK_ANALYSIS=true to use mock data instead.")
            self.mock_mode = True  # Fallback to mock mode if no API key
        elif self.api_key:
            try:
                # Initialize the Gemini API
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel('gemini-2.0-flash')

                # Log successful initialization
                masked_key = self.api_key[:5] + "..." + self.api_key[-5:]
                logger.info(f"✅ Gemini API Key initialized: {masked_key}")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini API: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                self.mock_mode = True  # Fallback to mock mode on initialization error

        if self.mock_mode:
            logger.info("✅ Running in MOCK mode - will return sample analysis")

    async def analyze_workout(self,
                             current_workout: Dict[str, Any],
                             historical_workouts: List[Dict[str, Any]],
                             user_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Comprehensive workout analysis using Gemini API or mock data

        Args:
            current_workout: The current completed workout
            historical_workouts: Previous user workouts for comparison
            user_info: Basic user information

        Returns:
            Detailed workout analysis or None if analysis fails
        """
        # Log analysis request
        logger.info(f"🏋️ Starting Workout Analysis Process for workout {current_workout.get('id')}")
        logger.info(f"👤 User: {user_info.get('username', 'Unknown')}")

        # Check if API key is available
        if not hasattr(self, 'api_key') or not self.api_key:
            logger.critical("API key not available during analyze_workout call")
            self.mock_mode = True

        # Use mock implementation if enabled or if API key is missing
        if self.mock_mode:
            logger.info(f"🤖 Using mock analysis data")
            return self._generate_mock_analysis(current_workout, historical_workouts)

        try:
            # Process workout data and build the optimized prompt
            logger.info("🔍 Processing workout data for analysis")

            # Process current workout exercises to extract statistics
            current_exercises = current_workout.get('exercises', [])
            exercise_stats = []

            for exercise in current_exercises:
                exercise_name = exercise.get('name', '')
                if exercise_name:
                    # Process historical data for this specific exercise
                    stats = self._process_exercise_history(
                        exercise_name,
                        exercise,
                        historical_workouts
                    )
                    exercise_stats.append(stats)

            # Process overall workout statistics
            workout_stats = self._process_workout_statistics(current_workout, historical_workouts)

            # Store processed data for visualization
            processed_data = {
                "exercise_stats": exercise_stats,
                "workout_stats": workout_stats
            }

            # Build the prompt with preprocessed data
            prompt = self._build_analysis_prompt(
                current_workout,
                historical_workouts,
                user_info
            )

            # Generate content using the Gemini API
            logger.info("🌐 Sending request to Gemini API")

            # Verify model is initialized
            if not hasattr(self, 'model'):
                logger.critical("Gemini model not initialized!")
                raise ValueError("Gemini model not initialized")

            # Set generation parameters
            generation_config = {
                "temperature": 0.2,
                "top_p": 0.95,
                "max_output_tokens": 1024,
            }

            # Call the Gemini API
            response = self.model.generate_content(
                prompt,
                generation_config=generation_config
            )

            # Extract and process the text response
            if response and hasattr(response, 'text'):
                generated_text = response.text

                logger.info("✅ Analysis Generated Successfully")

                # Parse the response into structured sections
                analysis_result = {
                    "summary": self._extract_section(generated_text, "SUMMARY"),
                    "progress": self._extract_section(generated_text, "PROGRESS"),
                    "insights": self._extract_section(generated_text, "INSIGHTS"),
                    "recommendations": self._extract_section(generated_text, "RECOMMENDATIONS"),
                    "raw_response": generated_text,
                    "processed_data": processed_data  # Include processed data for visualization
                }

                # Validate we have at least a summary
                if not analysis_result["summary"]:
                    logger.warning("⚠️ Generated analysis missing summary section")
                    analysis_result["summary"] = "Analysis completed, but no summary was generated."

                return analysis_result
            else:
                logger.warning("❌ No text response from Gemini API")
                return self._generate_mock_analysis(current_workout, historical_workouts)

        except Exception as e:
            logger.error(f"🔥 GEMINI ANALYSIS ERROR: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

            # Fallback to mock analysis on error
            logger.info("⚠️ Falling back to mock analysis due to error")
            return self._generate_mock_analysis(current_workout, historical_workouts)

    async def generate_workout_plan(self, prompt: str, formatted_exercises: str) -> Optional[Dict[str, Any]]:
        """
        Generate a structured workout plan using the Gemini API.

        Args:
            prompt: The user's natural language request for a workout plan.
            formatted_exercises: A formatted string listing available exercises.

        Returns:
            A dictionary representing the structured workout plan, or None if generation fails.
        """
        logger.info(f"💪 Starting Workout Plan Generation for prompt: '{prompt}'")

        if self.mock_mode:
            logger.info("🤖 Using mock workout plan data")
            # Return a structured mock plan for testing
            return {
                "plan_name": "Mock 4-Week Muscle Building Plan",
                "duration_days": 28,
                "description": "This is a mock plan designed to build muscle over four weeks.",
                "workouts": [
                    {
                        "day": 1,
                        "muscle_group": "Chest & Triceps",
                        "exercises": [
                            {"name": "Bench Press", "sets": 3, "reps": 8},
                            {"name": "Incline Dumbbell Press", "sets": 3, "reps": 10},
                            {"name": "Tricep Pushdown", "sets": 3, "reps": 12},
                        ],
                    },
                    {
                        "day": 2,
                        "muscle_group": "Back & Biceps",
                        "exercises": [
                            {"name": "Pull Ups", "sets": 3, "reps": "AMRAP"},
                            {"name": "Barbell Row", "sets": 3, "reps": 8},
                            {"name": "Bicep Curl", "sets": 3, "reps": 12},
                        ],
                    },
                ],
            }

        if not hasattr(self, 'model'):
            logger.critical("Gemini model not initialized!")
            return None

        generation_prompt = f\"\"\"
You are an expert personal trainer. A user has requested a workout plan with the following goal: "{prompt}".

Based on this goal and the list of available exercises below, create a structured workout plan for them.

**Available Exercises:**
{formatted_exercises}

**Instructions:**
1.  Analyze the user's request to determine the plan's structure (e.g., duration, frequency, muscle group splits).
2.  Create a workout plan that logically progresses and targets the user's goals.
3.  Return the plan as a single JSON object. Do not include any text or formatting outside of the JSON object.
4.  The JSON object must follow this exact structure:
    {{
      "plan_name": "Descriptive Name of the Plan",
      "duration_days": <total number of days for the plan, e.g., 30>,
      "description": "A brief explanation of the plan's focus and structure.",
      "workouts": [
        {{
          "day": <day number, e.g., 1>,
          "muscle_group": "Primary Muscle Group for the Day",
          "exercises": [
            {{
              "name": "Exercise Name from the list",
              "sets": <number of sets>,
              "reps": <number of reps or "AMRAP">
            }},
            ...
          ]
        }},
        ...
      ]
    }}

**Important:** Ensure the exercise names in your response exactly match the names from the "Available Exercises" list. Only return the JSON object.
\"\"\"

        try:
            logger.info("🌐 Sending workout plan request to Gemini API")
            response = self.model.generate_content(
                generation_prompt,
                generation_config={{
                    "temperature": 0.3,
                    "response_mime_type": "application/json",
                }}
            )

            if response and hasattr(response, 'text'):
                generated_text = response.text
                logger.info("✅ Workout Plan Generated Successfully")

                # The response should be a clean JSON string, but let's be safe
                # and extract it if it's embedded in markdown
                import json
                import re
                
                json_match = re.search(r'```json\\n(.*)\\n```', generated_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    json_str = generated_text

                plan_data = json.loads(json_str)
                return plan_data
            else:
                logger.warning("❌ No text response from Gemini API for workout plan")
                return None

        except Exception as e:
            logger.error(f"🔥 GEMINI PLAN GENERATION ERROR: {{str(e)}}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _generate_mock_analysis(self,
                              current_workout: Dict[str, Any],
                              historical_workouts: List[Dict[str, Any]]
                             ) -> Dict[str, Any]:
        """
        Generate mock analysis when API key is not available

        Args:
            current_workout: Current workout data
            historical_workouts: Previous workouts

        Returns:
            Mock analysis data
        """
        logger.info("🎭 Generating mock analysis data")

        # Extract basic workout info
        workout_id = current_workout.get('id', 0)
        exercise_count = len(current_workout.get('exercises', []))

        # Count total sets
        total_sets = sum(len(exercise.get('sets', []))
                         for exercise in current_workout.get('exercises', []))

        # Get exercise types
        exercise_types = [ex.get('exercise_type') for ex in current_workout.get('exercises', [])]
        has_strength = 'strength' in exercise_types
        has_cardio = 'cardio' in exercise_types

        # Process data for visualization even in mock mode
        try:
            # Process current workout exercises
            current_exercises = current_workout.get('exercises', [])
            exercise_stats = []

            for exercise in current_exercises:
                exercise_name = exercise.get('name', '')
                if exercise_name:
                    try:
                        # Process historical data for this specific exercise
                        stats = self._process_exercise_history(
                            exercise_name,
                            exercise,
                            historical_workouts
                        )
                        exercise_stats.append(stats)
                    except Exception as ex:
                        logger.warning(f"Error processing exercise {exercise_name}: {ex}")
                        # Add a simplified stat object for this exercise
                        exercise_stats.append({
                            'name': exercise_name,
                            'history_found': False,
                            'error': str(ex)
                        })

            try:
                # Process overall workout statistics with error handling
                workout_stats = self._process_workout_statistics(current_workout, historical_workouts)
            except Exception as ex:
                logger.warning(f"Error processing overall workout statistics: {ex}")
                workout_stats = {
                    'total_workouts': len(historical_workouts),
                    'error': str(ex)
                }

            # Store processed data for visualization
            processed_data = {
                "exercise_stats": exercise_stats,
                "workout_stats": workout_stats
            }
        except Exception as e:
            logger.warning(f"Error processing data for visualization in mock mode: {e}")
            processed_data = {
                "error": "Failed to process data for visualization",
                "message": str(e)
            }

        # Create personalized mock analysis
        summary = (
            f"You completed a workout with {exercise_count} exercises and {total_sets} sets total. "
            f"This workout was {'balanced between strength and cardio' if has_strength and has_cardio else 'focused on ' + (exercise_types[0] if exercise_types else 'general fitness')}. "
            f"The intensity level appears to be moderate based on your recorded data."
        )

        progress = (
            f"Compared to your previous {len(historical_workouts)} workouts, "
            f"you're maintaining consistency in your training schedule. "
            f"Your workout frequency is good, which is important for long-term progress. "
            f"Continue with this level of commitment to see ongoing improvements."
        )

        insights = (
            f"Your current exercise selection provides good coverage for {'multiple' if len(exercise_types) > 1 else 'specific'} fitness aspects. "
            f"{'Your strength training is balanced across different muscle groups.' if has_strength else ''} "
            f"{'Your cardio work is contributing to improved endurance.' if has_cardio else ''} "
            f"The workout duration appears appropriate for your current fitness level."
        )

        recommendations = (
            f"Consider {'adding more variety to your exercises' if exercise_count < 4 else 'maintaining your current exercise variety'}. "
            f"{'Try incorporating more cardio for improved heart health.' if not has_cardio else ''} "
            f"{'Include more strength training for muscle development.' if not has_strength else ''} "
            f"Gradually increase intensity over time to continue making progress. "
            f"Remember to include rest days in your weekly schedule for recovery."
        )

        logger.info(f"📝 Mock analysis generated for workout {workout_id}")

        return {
            "summary": summary,
            "progress": progress,
            "insights": insights,
            "recommendations": recommendations,
            "raw_response": "MOCK_DATA",
            "processed_data": processed_data  # Include processed data for visualization
        }

    def _extract_section(self, text: str, section: str) -> str:
        """
        Safely extract a section from the generated text

        Args:
            text: Full generated text
            section: Section to extract (e.g., "SUMMARY")

        Returns:
            Extracted section text or empty string
        """
        try:
            # Find section, handling potential variations in formatting
            section_marker = f"{section}:"

            # Try different case variations
            variations = [
                section_marker.upper(),
                section_marker.lower(),
                section_marker.capitalize()
            ]

            section_start = -1
            for variant in variations:
                pos = text.find(variant)
                if pos != -1:
                    section_start = pos
                    section_marker = variant
                    break

            if section_start == -1:
                logger.warning(f"Section {section} not found in generated text")
                return ""

            # Find next section or end of text
            next_sections = ["SUMMARY:", "PROGRESS:", "INSIGHTS:", "RECOMMENDATIONS:"]
            next_sections = [s.upper() for s in next_sections if s.upper() != section_marker.upper()]

            # Find the next section or use end of text
            next_section_positions = []
            for next_sec in next_sections:
                for variant in [next_sec.upper(), next_sec.lower(), next_sec.capitalize()]:
                    pos = text.find(variant, section_start + len(section_marker))
                    if pos != -1:
                        next_section_positions.append(pos)

            end_pos = min(next_section_positions) if next_section_positions else len(text)

            # Extract section text
            section_text = text[section_start + len(section_marker):end_pos].strip()

            logger.debug(f"Extracted {section} section ({len(section_text)} chars)")
            return section_text

        except Exception as e:
            logger.error(f"Section extraction error for {section}: {e}")
            return ""

    def _process_exercise_history(
        self,
        exercise_name: str,
        current_exercise: Dict[str, Any],  # Keeping for future use
        historical_workouts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process historical data for a specific exercise to generate statistics and trends.

        Args:
            exercise_name: Name of the exercise to analyze
            current_exercise: Current exercise data
            historical_workouts: All historical workouts

        Returns:
            Dictionary containing processed statistics for the exercise
        """
        # Process exercise history

        # Initialize data structures
        history = []
        dates = []
        weights = []
        reps = []
        volumes = []  # weight * reps

        # Extract all historical instances of this exercise
        for workout in historical_workouts:
            workout_date = workout.get('date')

            # Convert string date to datetime if needed
            parsed_date = None
            if isinstance(workout_date, str):
                try:
                    parsed_date = datetime.fromisoformat(workout_date.replace('Z', '+00:00'))
                except (ValueError, TypeError) as e:
                    logger.warning(f"Could not parse date string: {workout_date}, error: {e}")
                    continue  # Skip this workout if date can't be parsed
            elif isinstance(workout_date, datetime):
                parsed_date = workout_date
            else:
                continue  # Skip if no valid date

            for ex in workout.get('exercises', []):
                # Case-insensitive comparison to handle naming variations
                if ex.get('name', '').lower() == exercise_name.lower():
                    for s in ex.get('sets', []):
                        set_weight = s.get('weight')
                        set_reps = s.get('reps')

                        # Only include sets with both weight and reps for strength exercises
                        if set_weight is not None and set_reps is not None:
                            history.append({
                                'date': parsed_date,
                                'weight': set_weight,
                                'reps': set_reps,
                                'volume': set_weight * set_reps
                            })
                            dates.append(parsed_date)
                            weights.append(set_weight)
                            reps.append(set_reps)
                            volumes.append(set_weight * set_reps)

        # If no history found
        if not history:
            return {
                'name': exercise_name,
                'total_sets': 0,
                'history_found': False,
                'message': "No previous history found for this exercise."
            }

        # Sort history by date
        history.sort(key=lambda x: x['date'])

        # Calculate statistics
        stats = {
            'name': exercise_name,
            'history_found': True,
            'total_sets': len(history),
            'first_recorded': {
                'date': history[0]['date'],
                'weight': history[0]['weight'],
                'reps': history[0]['reps']
            },
            'most_recent': {
                'date': history[-1]['date'],
                'weight': history[-1]['weight'],
                'reps': history[-1]['reps']
            },
            'max_weight': max(weights),
            'max_reps': max(reps),
            'max_volume': max(volumes),
            'avg_weight': sum(weights) / len(weights),
            'avg_reps': sum(reps) / len(reps),
            'workout_frequency': len(set(dates))
        }

        # Calculate progress metrics
        if len(history) > 1:
            # Weight progress
            first_weight = history[0]['weight']
            last_weight = history[-1]['weight']
            weight_change = last_weight - first_weight
            weight_change_pct = (weight_change / first_weight * 100) if first_weight > 0 else 0

            # Volume progress
            first_volume = history[0]['volume']
            last_volume = history[-1]['volume']
            volume_change = last_volume - first_volume
            volume_change_pct = (volume_change / first_volume * 100) if first_volume > 0 else 0

            # Calculate days tracked
            days_tracked = 0
            if isinstance(history[-1]['date'], datetime) and isinstance(history[0]['date'], datetime):
                days_tracked = (history[-1]['date'] - history[0]['date']).days

            # Add to stats
            stats['progress'] = {
                'weight_change': weight_change,
                'weight_change_pct': weight_change_pct,
                'volume_change': volume_change,
                'volume_change_pct': volume_change_pct,
                'days_tracked': days_tracked
            }

            # Detect plateaus (no improvement in last 3 instances)
            if len(weights) >= 3 and all(weights[-1] <= weights[-i] for i in range(1, 4)):
                stats['plateau_detected'] = True
            else:
                stats['plateau_detected'] = False

            # Calculate trend (last 5 instances vs previous 5)
            if len(weights) >= 10:
                recent_avg = sum(weights[-5:]) / 5
                previous_avg = sum(weights[-10:-5]) / 5
                trend_pct = ((recent_avg - previous_avg) / previous_avg * 100) if previous_avg > 0 else 0
                stats['recent_trend_pct'] = trend_pct

        return stats

    def _process_workout_statistics(
        self,
        current_workout: Dict[str, Any],  # Keeping for future use
        historical_workouts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process overall workout statistics and trends

        Args:
            current_workout: Current workout data
            historical_workouts: All historical workouts

        Returns:
            Dictionary containing processed workout statistics
        """
        # Initialize statistics
        stats = {
            'total_workouts': len(historical_workouts),
            'exercise_type_distribution': defaultdict(int),
            'workout_frequency': {}
        }

        # Skip if no history
        if not historical_workouts:
            return stats

        # Get all workout dates
        dates = [w.get('date') for w in historical_workouts if w.get('date')]

        # Convert string dates to datetime objects if needed
        parsed_dates = []
        for date_str in dates:
            if isinstance(date_str, str):
                try:
                    # Try to parse the date string
                    parsed_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    parsed_dates.append(parsed_date)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Could not parse date string: {date_str}, error: {e}")
            elif isinstance(date_str, datetime):
                parsed_dates.append(date_str)

        # Sort the parsed dates
        parsed_dates.sort()

        if parsed_dates:
            stats['first_workout_date'] = parsed_dates[0].isoformat()
            stats['most_recent_workout_date'] = parsed_dates[-1].isoformat()

            # Calculate date range
            date_range_days = (parsed_dates[-1] - parsed_dates[0]).days
            stats['tracking_period_days'] = date_range_days

            if date_range_days > 0:
                # Calculate workout frequency
                stats['workouts_per_week'] = (len(dates) / (date_range_days / 7))

                # Calculate monthly frequency
                months = defaultdict(int)
                for date in parsed_dates:  # Use parsed_dates instead of dates
                    if isinstance(date, datetime):
                        month_key = f"{date.year}-{date.month}"
                        months[month_key] += 1

                # Get monthly averages if we have data
                if months:
                    monthly_counts = list(months.values())
                    stats['avg_workouts_per_month'] = sum(monthly_counts) / len(monthly_counts)
                    stats['max_workouts_in_month'] = max(monthly_counts)

        # Calculate exercise type distribution
        for workout in historical_workouts:
            for exercise in workout.get('exercises', []):
                ex_type = exercise.get('exercise_type', 'unknown')
                stats['exercise_type_distribution'][ex_type] += 1

        # Convert defaultdict to regular dict for JSON serialization
        stats['exercise_type_distribution'] = dict(stats['exercise_type_distribution'])

        return stats

    def _build_analysis_prompt(
        self,
        current_workout: Dict[str, Any],
        historical_workouts: List[Dict[str, Any]],
        user_info: Dict[str, Any]
    ) -> str:
        """
        Build a data-focused prompt for Gemini to interpret preprocessed workout statistics
        """
        # Build analysis prompt with preprocessed data

        # Process current workout exercises
        current_exercises = current_workout.get('exercises', [])
        exercise_stats = []

        for exercise in current_exercises:
            exercise_name = exercise.get('name', '')
            if exercise_name:
                # Process historical data for this specific exercise
                stats = self._process_exercise_history(
                    exercise_name,
                    exercise,
                    historical_workouts
                )
                exercise_stats.append(stats)

        # Process overall workout statistics
        workout_stats = self._process_workout_statistics(current_workout, historical_workouts)

        # Build the new prompt with preprocessed data
        prompt = """
    You are an elite personal trainer and performance analyst specializing in data interpretation. You are obnoxiously positive and motivating. Your task is to interpret the preprocessed workout statistics and provide meaningful insights and recommendations.

    The data has already been analyzed on the backend - your job is to interpret these statistics and explain what they mean for the user's fitness journey. Focus on comparing the CURRENT WORKOUT to historical data and identifying long-term trends.

    Use these four sections to structure your analysis:

    SUMMARY:
    - Describe the current workout clearly and briefly.
    - Highlight how this workout compares to the user's typical workouts.
    - Note any personal records or achievements in this specific workout.

    PROGRESS:
    - Compare the current workout's weights/reps to the user's historical averages.
    - Highlight specific exercises where the user performed better or worse than their average.
    - Put the numbers in context - what do the percentages and trends mean for the user?

    INSIGHTS:
    - Identify patterns between this workout and the user's training history.
    - Point out any imbalances or gaps in their current approach.
    - Connect the statistical data to practical training implications.

    RECOMMENDATIONS:
    - Suggest specific, actionable next steps based on how this workout went.
    - Provide guidance on how to address plateaus or accelerate progress.
    - Offer training strategy adjustments that align with the data trends.

    FORMAT RULES:
    - Use each of the 4 headers exactly once in all caps, followed by a colon.
    - Each section should be 3–5 sentences and go beyond the obvious.
    - Be direct, constructive, and specific. Avoid generic praise.
    - Reference specific numbers from the data to support your points.
    - Always compare the CURRENT workout to historical data - this is the key focus.
    """

        # Add current workout details
        prompt += "\n\nCURRENT WORKOUT:"
        prompt += f"\nID: {current_workout.get('id')}"
        prompt += f"\nDate: {current_workout.get('date')}"
        if current_workout.get("description"):
            prompt += f"\nUser Notes: {current_workout.get('description')}"

        exercises = current_workout.get('exercises', [])
        prompt += f"\n\nEXERCISES ({len(exercises)}):"
        for i, exercise in enumerate(exercises):
            ex_type = exercise.get("exercise_type", "").capitalize()
            prompt += f"\n  {i+1}. {exercise.get('name')} ({ex_type})"
            for j, s in enumerate(exercise.get('sets', [])):
                details = []
                if s.get('reps'): details.append(f"{s['reps']} reps")
                if s.get('weight'): details.append(f"{s['weight']} lbs")
                if s.get('duration_seconds'): details.append(f"{s['duration_seconds']} sec")
                if s.get('distance'): details.append(f"{s['distance']} mi")
                if s.get('notes'): details.append(f"Note: {s['notes']}")
                if details:
                    prompt += f"\n    - Set {j+1}: " + ", ".join(details)

        # Add preprocessed exercise statistics
        prompt += "\n\nPREPROCESSED EXERCISE STATISTICS:"
        for stat in exercise_stats:
            prompt += f"\n\nExercise: {stat['name']}"

            if not stat.get('history_found', False):
                prompt += f"\n  No previous history found for this exercise."
                continue

            prompt += f"\n  Total Previous Sets: {stat['total_sets']}"
            prompt += f"\n  First Recorded: {stat['first_recorded']['date']} - {stat['first_recorded']['weight']} lbs × {stat['first_recorded']['reps']} reps"
            prompt += f"\n  Most Recent: {stat['most_recent']['date']} - {stat['most_recent']['weight']} lbs × {stat['most_recent']['reps']} reps"
            prompt += f"\n  Personal Records:"
            prompt += f"\n    - Max Weight: {stat['max_weight']} lbs"
            prompt += f"\n    - Max Reps: {stat['max_reps']}"
            prompt += f"\n    - Max Volume: {stat['max_volume']} (weight × reps)"

            if 'progress' in stat:
                days = stat['progress']['days_tracked']
                prompt += f"\n  Progress Over {days} days:"
                prompt += f"\n    - Weight Change: {stat['progress']['weight_change']:.1f} lbs ({stat['progress']['weight_change_pct']:.1f}%)"
                prompt += f"\n    - Volume Change: {stat['progress']['volume_change']:.1f} ({stat['progress']['volume_change_pct']:.1f}%)"

            if 'plateau_detected' in stat:
                if stat['plateau_detected']:
                    prompt += f"\n  Plateau Detected: Yes (no improvement in recent sessions)"
                else:
                    prompt += f"\n  Plateau Detected: No (still making progress)"

            if 'recent_trend_pct' in stat:
                trend = stat['recent_trend_pct']
                trend_direction = "upward" if trend > 0 else "downward" if trend < 0 else "flat"
                prompt += f"\n  Recent Trend: {trend_direction} ({trend:.1f}%)"

        # Add overall workout statistics
        prompt += "\n\nOVERALL WORKOUT STATISTICS:"
        prompt += f"\n  Total Workouts: {workout_stats['total_workouts']}"

        if 'tracking_period_days' in workout_stats:
            days = workout_stats['tracking_period_days']
            prompt += f"\n  Tracking Period: {days} days"

        if 'workouts_per_week' in workout_stats:
            prompt += f"\n  Average Frequency: {workout_stats['workouts_per_week']:.1f} workouts per week"

        if 'exercise_type_distribution' in workout_stats and workout_stats['exercise_type_distribution']:
            prompt += "\n  Exercise Type Distribution:"
            total = sum(workout_stats['exercise_type_distribution'].values())
            for ex_type, count in workout_stats['exercise_type_distribution'].items():
                percentage = (count / total) * 100 if total > 0 else 0
                prompt += f"\n    - {ex_type.capitalize()}: {count} exercises ({percentage:.1f}%)"

        # Add user info context
        prompt += "\n\nUSER PROFILE:"
        prompt += f"\nUsername: {user_info.get('username', 'Unknown')}"
        prompt += f"\nTotal Completed Workouts: {user_info.get('total_workouts', 0)}"
        if user_info.get('first_workout_date'):
            prompt += f"\nFirst Workout: {user_info.get('first_workout_date')}"
        if user_info.get('average_workouts_per_week'):
            prompt += f"\nAvg Workouts Per Week: {user_info.get('average_workouts_per_week'):.2f}"

        prompt += "\n\nNow interpret these statistics and generate your analysis using the structure above."
        return prompt