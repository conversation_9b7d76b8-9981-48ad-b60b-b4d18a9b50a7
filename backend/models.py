# backend/models.py
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, DateTime, Text, Float, Enum, Table, JSON
from sqlalchemy.orm import relationship
from database import Base
from datetime import datetime
import enum
from utils.timezone_utils import get_eastern_now

# Many-to-many relationship table for exercise-muscle groups
exercise_muscle_groups = Table(
    'exercise_muscle_groups',
    Base.metadata,
    Column('exercise_id', Integer, ForeignKey('exercise_library.id', ondelete="CASCADE"), primary_key=True),
    Column('muscle_group_id', Integer, ForeignKey('muscle_groups.id', ondelete="CASCADE"), primary_key=True)
)

# Many-to-many relationship table for exercise-equipment
exercise_equipment = Table(
    'exercise_equipment',
    Base.metadata,
    Column('exercise_id', Integer, ForeignKey('exercise_library.id', ondelete="CASCADE"), primary_key=True),
    Column('equipment_id', Integer, <PERSON><PERSON>ey('equipment.id', ondelete="CASCADE"), primary_key=True)
)

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)

    workouts = relationship("Workout", back_populates="user", cascade="all, delete-orphan")
    workout_scores = relationship("WorkoutScore", back_populates="user")
    milestones = relationship("Milestone", back_populates="user")
    achievements = relationship("UserAchievement", back_populates="user")
    stats = relationship("UserStat", back_populates="user", uselist=False)
    weight_entries = relationship("UserWeight", back_populates="user", cascade="all, delete-orphan")
    workout_plans = relationship("WorkoutPlan", back_populates="user", cascade="all, delete-orphan")


class WorkoutStatus(str, enum.Enum):
    in_progress = "in_progress"
    completed = "completed"


class Workout(Base):
    __tablename__ = "workouts"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime, default=get_eastern_now)
    description = Column(Text, nullable=True)
    status = Column(String, default=WorkoutStatus.completed)
    user_id = Column(Integer, ForeignKey("users.id"))
    planned_workout_id = Column(Integer, ForeignKey("planned_workouts.id"), nullable=True)  # Link to planned workout

    user = relationship("User", back_populates="workouts")
    exercises = relationship("Exercise", back_populates="workout", cascade="all, delete-orphan")
    score = relationship("WorkoutScore", back_populates="workout", uselist=False)
    planned_workout = relationship("PlannedWorkout", foreign_keys=[planned_workout_id])


class ExerciseType(str, enum.Enum):
    strength = "strength"
    cardio = "cardio"
    flexibility = "flexibility"
    other = "other"


class Exercise(Base):
    __tablename__ = "exercises"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    exercise_type = Column(String, default=ExerciseType.strength)
    workout_id = Column(Integer, ForeignKey("workouts.id", ondelete="CASCADE"))

    workout = relationship("Workout", back_populates="exercises")
    sets = relationship("ExerciseSet", back_populates="exercise", cascade="all, delete-orphan")


class ExerciseSet(Base):
    __tablename__ = "exercise_sets"

    id = Column(Integer, primary_key=True, index=True)
    reps = Column(Integer, nullable=True)
    weight = Column(Float, nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    distance = Column(Float, nullable=True)
    notes = Column(Text, nullable=True)
    exercise_id = Column(Integer, ForeignKey("exercises.id", ondelete="CASCADE"))

    exercise = relationship("Exercise", back_populates="sets")


class MuscleGroup(Base):
    __tablename__ = "muscle_groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True)

    # Relationship to exercises
    exercises = relationship(
        "ExerciseLibrary",
        secondary=exercise_muscle_groups,
        back_populates="muscle_groups"
    )


class Equipment(Base):
    __tablename__ = "equipment"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True)

    # Relationship to exercises
    exercises = relationship(
        "ExerciseLibrary",
        secondary=exercise_equipment,
        back_populates="equipment"
    )


class ExerciseLibrary(Base):
    __tablename__ = "exercise_library"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)
    exercise_type = Column(String, nullable=False)  # strength, cardio, flexibility, other
    difficulty = Column(String, nullable=True)  # beginner, intermediate, advanced

    # Relationships
    muscle_groups = relationship(
        "MuscleGroup",
        secondary=exercise_muscle_groups,
        back_populates="exercises"
    )

    equipment = relationship(
        "Equipment",
        secondary=exercise_equipment,
        back_populates="exercises"
    )

    # Form analysis rules
    form_rules = relationship("FormRules", back_populates="exercise", uselist=False)


class FormAnalysisStatus(str, enum.Enum):
    """Status of a form analysis session"""
    in_progress = "in_progress"
    completed = "completed"
    failed = "failed"


class FormAnalysis(Base):
    """Model for storing form analysis results"""
    __tablename__ = "form_analysis"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    exercise_id = Column(Integer, ForeignKey("exercise_library.id"))
    date = Column(DateTime, default=get_eastern_now)
    status = Column(String, default=FormAnalysisStatus.in_progress)

    # Overall scores
    overall_score = Column(Float, nullable=True)

    # Detailed analysis results stored as JSON
    analysis_data = Column(JSON, nullable=True)

    # Feedback and recommendations
    feedback = Column(Text, nullable=True)
    recommendations = Column(Text, nullable=True)

    # Relationships
    user = relationship("User")
    exercise = relationship("ExerciseLibrary")
    issues = relationship("FormIssue", back_populates="form_analysis", cascade="all, delete-orphan")


class FormIssue(Base):
    """Model for storing specific form issues detected during analysis"""
    __tablename__ = "form_issues"

    id = Column(Integer, primary_key=True, index=True)
    form_analysis_id = Column(Integer, ForeignKey("form_analysis.id", ondelete="CASCADE"))
    issue_type = Column(String, nullable=False)
    severity = Column(Float, nullable=False)  # 0-1 scale
    frame_number = Column(Integer, nullable=True)  # Which frame in the video
    description = Column(Text, nullable=False)
    recommendation = Column(Text, nullable=True)

    # Relationship
    form_analysis = relationship("FormAnalysis", back_populates="issues")


class FormRules(Base):
    """Model for storing form analysis rules for specific exercises"""
    __tablename__ = "form_rules"

    id = Column(Integer, primary_key=True, index=True)
    exercise_id = Column(Integer, ForeignKey("exercise_library.id", ondelete="CASCADE"), unique=True)

    # Joint angle thresholds
    knee_min_angle = Column(Float, nullable=True)
    knee_max_angle = Column(Float, nullable=True)
    hip_min_angle = Column(Float, nullable=True)
    hip_max_angle = Column(Float, nullable=True)
    back_min_angle = Column(Float, nullable=True)
    back_max_angle = Column(Float, nullable=True)

    # Position thresholds
    knee_tracking_threshold = Column(Float, nullable=True)  # Max deviation in degrees
    bar_path_threshold = Column(Float, nullable=True)  # Max horizontal deviation in inches

    # Additional rules as JSON
    additional_rules = Column(JSON, nullable=True)

    # Relationship
    exercise = relationship("ExerciseLibrary", back_populates="form_rules")


class MilestoneStatus(str, enum.Enum):
    """Status of a milestone"""
    in_progress = "in_progress"
    completed = "completed"


class MilestoneType(str, enum.Enum):
    """Types of milestones"""
    weekly_workouts = "weekly_workouts"
    monthly_workouts = "monthly_workouts"
    yearly_workouts = "yearly_workouts"
    consecutive_days = "consecutive_days"
    exercise_variety = "exercise_variety"


class Milestone(Base):
    """Model for tracking user progress toward goals"""
    __tablename__ = "milestones"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    milestone_type = Column(String, nullable=False)
    target_value = Column(Integer, nullable=False)
    current_value = Column(Integer, default=0)
    status = Column(String, default=MilestoneStatus.in_progress)
    start_date = Column(DateTime, default=get_eastern_now)
    end_date = Column(DateTime, nullable=True)
    completed_date = Column(DateTime, nullable=True)
    points_awarded = Column(Integer, default=0)

    # Relationship
    user = relationship("User")


class Achievement(Base):
    """Model for defining achievements users can earn"""
    __tablename__ = "achievements"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    icon = Column(String, nullable=True)
    points = Column(Integer, default=0)


class UserAchievement(Base):
    """Model for tracking which achievements users have earned"""
    __tablename__ = "user_achievements"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    achievement_id = Column(Integer, ForeignKey("achievements.id"))
    date_earned = Column(DateTime, default=get_eastern_now)

    # Relationships
    user = relationship("User")
    achievement = relationship("Achievement")


class UserStat(Base):
    """Model for tracking aggregated user statistics"""
    __tablename__ = "user_stats"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    total_score = Column(Integer, default=0)
    current_streak = Column(Integer, default=0)
    longest_streak = Column(Integer, default=0)
    total_workouts = Column(Integer, default=0)
    unique_exercises = Column(Integer, default=0)
    last_updated = Column(DateTime, default=get_eastern_now)

    # Relationship
    user = relationship("User")


class UserWeight(Base):
    """Model for tracking user weight and body fat history"""
    __tablename__ = "user_weights"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    weight = Column(Float, nullable=False)
    body_fat = Column(Float, nullable=True)  # Body fat percentage (optional)
    date = Column(DateTime, default=get_eastern_now)
    notes = Column(Text, nullable=True)

    # Relationship
    user = relationship("User", back_populates="weight_entries")


class WorkoutScore(Base):
    """Model for storing scores for completed workouts"""
    __tablename__ = "workout_scores"

    id = Column(Integer, primary_key=True, index=True)
    workout_id = Column(Integer, ForeignKey("workouts.id", ondelete="CASCADE"), unique=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    base_score = Column(Integer, default=0)
    streak_bonus = Column(Integer, default=0)
    variety_bonus = Column(Integer, default=0)
    new_exercise_bonus = Column(Integer, default=0)
    total_score = Column(Integer, default=0)
    date = Column(DateTime, default=get_eastern_now)
    details = Column(JSON, nullable=True)

    # Relationships
    workout = relationship("Workout")
    user = relationship("User")


class WorkoutPlanStatus(str, enum.Enum):
    """Status of a workout plan"""
    active = "active"
    completed = "completed"
    paused = "paused"
    cancelled = "cancelled"


class WorkoutPlan(Base):
    """Model for storing user workout plans"""
    __tablename__ = "workout_plans"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    status = Column(String, default=WorkoutPlanStatus.active)
    raw_text = Column(Text, nullable=True)  # Store original LLM response

    # Relationships
    user = relationship("User", back_populates="workout_plans")
    planned_workouts = relationship("PlannedWorkout", back_populates="workout_plan", cascade="all, delete-orphan")


class PlannedWorkoutStatus(str, enum.Enum):
    """Status of a planned workout"""
    scheduled = "scheduled"
    completed = "completed"
    skipped = "skipped"


class PlannedWorkout(Base):
    """Model for storing individual planned workouts within a workout plan"""
    __tablename__ = "planned_workouts"

    id = Column(Integer, primary_key=True, index=True)
    workout_plan_id = Column(Integer, ForeignKey("workout_plans.id"))
    workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=True)  # Links to actual completed workout
    planned_date = Column(DateTime, nullable=False)
    status = Column(String, default=PlannedWorkoutStatus.scheduled)
    day_of_plan = Column(Integer, nullable=False)  # Day 1, 2, 3, etc. of the plan
    exercises = Column(JSON, nullable=False)  # Planned exercises as JSON

    # Relationships
    workout_plan = relationship("WorkoutPlan", back_populates="planned_workouts")
    workout = relationship("Workout")  # Links to actual completed workout if any