#!/usr/bin/env python3
"""
Production Migration Script for Workout Planning Feature
========================================================

This script migrates the production database to support the new workout planning feature.
It creates all necessary tables and columns for:
- Workout Plans (user-created workout schedules)
- Planned Workouts (individual workouts within a plan)
- Workout-to-Plan linking (connects completed workouts to their planned counterparts)

USAGE:
    python workout_planning_migration.py

REQUIREMENTS:
    - psycopg2-binary (pip install psycopg2-binary)
    - Access to PostgreSQL database

ENVIRONMENT VARIABLES:
    - DATABASE_URL (optional, defaults to production format)
    - DB_HOST (default: localhost)
    - DB_PORT (default: 5432)
    - DB_NAME (default: workout_db)
    - DB_USER (default: postgres)
    - DB_PASSWORD (required)
"""

import os
import sys
import logging
from datetime import datetime

try:
    import psycopg2
    from psycopg2 import sql
except ImportError:
    print("❌ Error: psycopg2 is required. Install with: pip install psycopg2-binary")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class WorkoutPlanningMigration:
    def __init__(self):
        """Initialize migration with database connection"""
        self.conn = None
        self.cursor = None
        self._connect_to_database()
    
    def _connect_to_database(self):
        """Establish database connection"""
        try:
            # Try DATABASE_URL first (for production environments)
            database_url = os.getenv('DATABASE_URL')
            
            if database_url:
                logger.info("Connecting using DATABASE_URL...")
                self.conn = psycopg2.connect(database_url)
            else:
                # Fallback to individual environment variables
                db_config = {
                    'host': os.getenv('DB_HOST', 'localhost'),
                    'port': os.getenv('DB_PORT', '5432'),
                    'database': os.getenv('DB_NAME', 'workout_db'),
                    'user': os.getenv('DB_USER', 'postgres'),
                    'password': os.getenv('DB_PASSWORD')
                }
                
                if not db_config['password']:
                    logger.error("❌ DB_PASSWORD environment variable is required")
                    sys.exit(1)
                
                logger.info(f"Connecting to {db_config['host']}:{db_config['port']}/{db_config['database']}")
                self.conn = psycopg2.connect(**db_config)
            
            self.conn.autocommit = False
            self.cursor = self.conn.cursor()
            logger.info("✅ Database connection established")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            sys.exit(1)
    
    def _table_exists(self, table_name):
        """Check if a table exists"""
        self.cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            );
        """, (table_name,))
        return self.cursor.fetchone()[0]
    
    def _column_exists(self, table_name, column_name):
        """Check if a column exists in a table"""
        self.cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = %s 
                AND column_name = %s
            );
        """, (table_name, column_name))
        return self.cursor.fetchone()[0]
    
    def create_workout_plans_table(self):
        """Create the workout_plans table"""
        if self._table_exists('workout_plans'):
            logger.info("✅ Table 'workout_plans' already exists")
            return
        
        logger.info("🔧 Creating 'workout_plans' table...")
        self.cursor.execute("""
            CREATE TABLE workout_plans (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                name VARCHAR NOT NULL,
                description TEXT,
                start_date TIMESTAMP,
                end_date TIMESTAMP,
                status VARCHAR DEFAULT 'active',
                raw_text TEXT
            );
        """)
        
        # Create indexes
        self.cursor.execute("CREATE INDEX ix_workout_plans_id ON workout_plans(id);")
        
        logger.info("✅ Created 'workout_plans' table")
    
    def create_planned_workouts_table(self):
        """Create the planned_workouts table"""
        if self._table_exists('planned_workouts'):
            logger.info("✅ Table 'planned_workouts' already exists")
            return
        
        logger.info("🔧 Creating 'planned_workouts' table...")
        self.cursor.execute("""
            CREATE TABLE planned_workouts (
                id SERIAL PRIMARY KEY,
                workout_plan_id INTEGER REFERENCES workout_plans(id),
                workout_id INTEGER REFERENCES workouts(id),
                planned_date TIMESTAMP NOT NULL,
                status VARCHAR DEFAULT 'scheduled',
                day_of_plan INTEGER NOT NULL,
                exercises JSON NOT NULL
            );
        """)
        
        # Create indexes
        self.cursor.execute("CREATE INDEX ix_planned_workouts_id ON planned_workouts(id);")
        
        logger.info("✅ Created 'planned_workouts' table")
    
    def add_planned_workout_id_column(self):
        """Add planned_workout_id column to workouts table"""
        if self._column_exists('workouts', 'planned_workout_id'):
            logger.info("✅ Column 'planned_workout_id' already exists in workouts table")
            return
        
        logger.info("🔧 Adding 'planned_workout_id' column to workouts table...")
        self.cursor.execute("""
            ALTER TABLE workouts 
            ADD COLUMN planned_workout_id INTEGER 
            REFERENCES planned_workouts(id);
        """)
        
        logger.info("✅ Added 'planned_workout_id' column to workouts table")
    
    def verify_migration(self):
        """Verify that all migration steps completed successfully"""
        logger.info("🔍 Verifying migration...")
        
        checks = [
            ("workout_plans table", lambda: self._table_exists('workout_plans')),
            ("planned_workouts table", lambda: self._table_exists('planned_workouts')),
            ("planned_workout_id column", lambda: self._column_exists('workouts', 'planned_workout_id'))
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            if check_func():
                logger.info(f"✅ {check_name} - OK")
            else:
                logger.error(f"❌ {check_name} - FAILED")
                all_passed = False
        
        return all_passed
    
    def run_migration(self):
        """Execute the complete migration"""
        try:
            logger.info("🚀 Starting Workout Planning Migration")
            logger.info("=" * 50)
            
            # Execute migration steps
            self.create_workout_plans_table()
            self.create_planned_workouts_table()
            self.add_planned_workout_id_column()
            
            # Verify migration
            if self.verify_migration():
                self.conn.commit()
                logger.info("=" * 50)
                logger.info("✅ Migration completed successfully!")
                logger.info("🎯 Workout Planning feature is now ready for production")
            else:
                self.conn.rollback()
                logger.error("❌ Migration verification failed - rolling back changes")
                sys.exit(1)
                
        except Exception as e:
            self.conn.rollback()
            logger.error(f"❌ Migration failed: {e}")
            sys.exit(1)
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main entry point"""
    print("🏋️  Workout Tracker - Workout Planning Migration")
    print("=" * 60)
    print("This script will add workout planning functionality to your database.")
    print("It will create new tables and columns required for the feature.")
    print()
    
    # Confirm before proceeding
    response = input("Continue with migration? (y/N): ").strip().lower()
    if response != 'y':
        print("Migration cancelled.")
        sys.exit(0)
    
    # Run migration
    migration = WorkoutPlanningMigration()
    migration.run_migration()

if __name__ == "__main__":
    main()
